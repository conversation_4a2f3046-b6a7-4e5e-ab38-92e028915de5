// Import required Node.js modules
// const ari = require('ari-client'); // Asterisk REST Interface client
const { AriClient } = require('@ipcom/asterisk-ari');
const WebSocket = require('ws'); // WebSocket library for OpenAI real-time API
const fs = require('fs'); // File system module for saving audio files
const dgram = require('dgram'); // UDP datagram for RTP audio streaming
const winston = require('winston'); // Logging library
const chalk = require('chalk'); // Colorizes console output
const async = require('async'); // Async utilities (used for RTP queue)
require('dotenv').config(); // Loads environment variables from .env file

// Configuration constants loaded from environment variables or defaults

const ARI_HOST = process.env.ARI_HOST; // Asterisk ARI endpoint
const ARI_PORT = process.env.ARI_PORT;
const ARI_USER = process.env.ARI_USER; // ARI username
const ARI_PASS = process.env.ARI_PASS // ARI password
const ARI_APP = process.env.ARI_APP; // Stasis application name
const OPENAI_API_KEY = process.env.OPENAI_API_KEY; // OpenAI API key from .env
const REALTIME_URL = `wss://api.openai.com/v1/realtime?model=${process.env.MODEL}`; // OpenAI real-time WebSocket URL
const RTP_PORT = 12000; // Local port for RTP audio reception
const MAX_CALL_DURATION = process.env.MAX_CALL_DURATION ? parseInt(process.env.MAX_CALL_DURATION) : 300000; // Max call duration in ms (default: 5 min)
const RTP_QUEUE_CONCURRENCY = parseInt(process.env.RTP_QUEUE_CONCURRENCY) || 50; // Concurrent RTP packet sends
const LOG_RTP_EVERY_N_PACKETS = parseInt(process.env.LOG_RTP_EVERY_N_PACKETS) || 100; // Log RTP stats every N packets
const ENABLE_RTP_LOGGING = process.env.ENABLE_RTP_LOGGING === 'true'; // Enable detailed RTP logging
const ENABLE_SENT_TO_OPENAI_RECORDING = process.env.ENABLE_SENT_TO_OPENAI_RECORDING === 'true'; // Controls saving of both .raw and .wav files
const VAD_THRESHOLD = process.env.VAD_THRESHOLD ? parseFloat(process.env.VAD_THRESHOLD) : 0.1; // Voice Activity Detection threshold
const VAD_PREFIX_PADDING_MS = process.env.VAD_PREFIX_PADDING_MS ? parseInt(process.env.VAD_PREFIX_PADDING_MS) : 300; // VAD prefix padding in ms
const VAD_SILENCE_DURATION_MS = process.env.VAD_SILENCE_DURATION_MS ? parseInt(process.env.VAD_SILENCE_DURATION_MS) : 500; // VAD silence duration in ms
const TARGET_RMS = 0.15; // Target Root Mean Square for audio normalization
const MIN_RMS = 0.001; // Minimum RMS to apply gain
EXTERNAL_HOST_IP = process.env.EXTERNAL_HOST_IP;

// Counters for client/server event logging
let sentEventCounter = 0; // Tracks sent events to OpenAI
let receivedEventCounter = -1; // Tracks received events from OpenAI

// Configure Winston logger with timestamp and colorized output
const logger = winston.createLogger({
  level: 'info', // Log level
  format: winston.format.combine(
    winston.format.timestamp(), // Add timestamp to logs
    winston.format.printf(({ timestamp, level, message }) => {
      const [origin] = message.split(' ', 1); // Extract message origin (Client/Server)
      let counter;
      let coloredMessage;
      if (origin === '[Client]') {
        counter = `C-${sentEventCounter.toString().padStart(4, '0')}`; // Client event counter
        sentEventCounter++;
        coloredMessage = chalk.cyanBright(message); // Cyan for client messages
      } else if (origin === '[Server]') {
        counter = `S-${receivedEventCounter.toString().padStart(4, '0')}`; // Server event counter
        receivedEventCounter++;
        coloredMessage = chalk.yellowBright(message); // Yellow for server messages
      } else if (origin === '[RTP]') {
        counter = 'RTP'; // RTP specific logs
        coloredMessage = chalk.greenBright(message); // Green for RTP messages
      } else if (origin === '[AUDIO]') {
        counter = 'AUD'; // Audio processing logs
        coloredMessage = chalk.magentaBright(message); // Magenta for audio messages
      } else if (origin === '[DEBUG]') {
        counter = 'DBG'; // Debug logs
        coloredMessage = chalk.blueBright(message); // Blue for debug messages
      } else {
        counter = 'N/A'; // No counter for general logs
        coloredMessage = chalk.gray(message); // Gray for general logs
      }
      return `${counter} | ${timestamp} [${level.toUpperCase()}] ${coloredMessage}`; // Formatted log line
    })
  ),
  transports: [new winston.transports.Console()] // Output logs to console
});

// Helper functions for logging OpenAI events
const logClient = (msg) => logger.info(`[Client] ${msg}`); // Log client-side OpenAI events
const logServer = (msg) => logger.info(`[Server] ${msg}`); // Log server-side OpenAI events
const logRTP = (msg) => logger.info(`[RTP] ${msg}`); // Log RTP events
const logAudio = (msg) => logger.info(`[AUDIO] ${msg}`); // Log audio processing events
const logDebug = (msg) => logger.info(`[DEBUG] ${msg}`); // Log debug events

// Maps to track channel states and audio buffers
const extMap = new Map(); // Maps ExternalMedia channels to their bridges and SIP channels
const sipMap = new Map(); // Maps SIP channels to their WebSocket and bridge data
const rtpSender = dgram.createSocket('udp4'); // Single UDP socket for sending RTP packets
let rtpReceiver = dgram.createSocket('udp4'); // UDP socket for receiving RTP packets
let ariClient; // ARI client instance

const audioFromAsteriskMap = new Map(); // Buffers audio received from Asterisk
const audioToOpenAIMap = new Map(); // Buffers audio sent to OpenAI
const amplificationLogFrequency = new Map(); // Tracks last amplification log time per channel
const rmsLogFrequency = new Map(); // Tracks last RMS log time per channel
const rtpSentStats = new Map(); // Tracks RTP stats per channel

// Global RTP reception statistics
let globalRtpStats = {
  totalPackets: 0,
  totalBytes: 0,
  startTime: null,
  lastLogTime: null,
  sourcesDetected: new Set()
};

function get_org_instruct(organisationId) {
    return `You are a helpful and professional AI customer support assistant of ${organisationId} whose purpose
      is to answer customer queries accurately. Respond to customers' questions as if you are customer support
      for ${organisationId} itself — use "we", "our", "here at ${organisationId}", etc. Do NOT refer to ${organisationId} in third person.
     Make sure that NO bullet points are used. Answer in paragraph form. Keep the informative responses short and concise!
      Use handle_outof_scope_cases function for all user queries, DO NOT ANSWER ANY QUERY ON YOUR OWN.
     Keep the informative responses short and concise!
     Before calling a function, ALWAYS tell the user to wait please.
     Your response will be read aloud using speech-to-speech. Make sure your answer is clear, natural, and easy to understand when spoken. Do not use markdown, bullet points, or special formatting. If you mention any prices or amounts, write them in words instead of numbers for better pronunciation.
     Gender Neutrality Instructions:
     - Be completely gender-neutral in all replies.
     - Do not refer to yourself or the user as male or female.
     - NEVER use any gendered verb forms (e.g., "karta hoon", "karti hoon", "samajhta hoon", "samajhti hoon", "janta hoon", "jaanti hoon", etc.). Always use gender-neutral or impersonal phrasing, such as "yeh mumkin hai", "aap ke liye kiya ja sakta hai", or other constructions that do not reveal gender.
     - Do not use "sir", "madam", "baji", "bhai", "behn", etc.
     - Use terms like "aap" or "customer", 'Muaziz Saarif (معزز صارف)' instead. Avoid all gendered titles.
     Language and Tone Instructions:
     - Use everyday conversational tone.
     - In any language other than English, keep brand names, product names, and organization names in English — do not translate them.
     - Use natural, spoken language like you're talking to a customer or friend — avoid overly formal or literal translation.
     - If user uses Urdu, reply back in usual conversational Urdu, mixing in commonly used English terms (like "account", "internet", "router", installation etc.).
     - Avoid poetic, overly technical, or formal Urdu.
     - Keep it conversational and polite.
     IMPORTANT: You MUST respond in Urdu language. DO NOT RESPOND IN ANY OTHER LANGUAGE UNDER ANY CIRCUMSTANCES!
     NEVER mention language code or language name in the response!!!`
  }

  // Get default tools including handle_outof_scope_cases
  const getDefaultTools = () => [
    {
      type: "function",
      name: "handle_outof_scope_cases",
      description:
        "Use this function as a final resort if no other function is suitable for the users query. This is the primary tool for answering general knowledge questions, open-ended inquiries, or any request that falls outside the scope of other specialized functions. It queries a comprehensive knowledge base to find an answer.",
      parameters: {
        type: "object",
        properties: {
          user_query: {
            type: "string",
            description: "The users original, complete question or topic to search for in the knowledge base.",
          },
        },
        required: ["user_query"],
      },
    }
  ]

// Add an ExternalMedia channel to a bridge with retry logic
async function addExtToBridge(client, channel, bridgeId, retries = 5, delay = 500) {
  try {
    logDebug(`Adding ExternalMedia channel ${channel.id} to bridge ${bridgeId} (attempt ${6-retries})`);
    // FIXED: Use the correct method to add channel to bridge
    await client.bridges.addChannels(bridgeId, {
      channel: [channel.id]
    });
    logger.info(`ExternalMedia channel ${channel.id} added to bridge ${bridgeId}`);
  } catch (err) {
    logger.error(`Error adding ExternalMedia channel ${channel.id} to bridge ${bridgeId}: ${err.message}`);
    if (retries) {
      logger.info(`Retrying to add ExternalMedia channel ${channel.id} to bridge ${bridgeId} (${retries} attempts remaining)`);
      await new Promise(r => setTimeout(r, delay)); // Wait before retrying
      return addExtToBridge(client, channel, bridgeId, retries - 1, delay); // Recursive retry
    }
    throw err;
  }
}

function startRTPReceiver() {
  logRTP(`Initializing RTP receiver on port ${RTP_PORT}`);
  
  const audioBuffers = new Map();
  const BUFFER_INTERVAL_MS = 200;
  const channelTimeouts = new Map();

  // Test UDP socket binding with detailed error handling
  rtpReceiver.on('listening', () => {
    const address = rtpReceiver.address();
    logRTP(`✅ RTP Receiver successfully listening on ${address.address}:${address.port}`);
    globalRtpStats.startTime = Date.now();
    
    // Perform immediate connectivity test
    setTimeout(() => {
      testRTPConnectivity();
    }, 1000);
  });

  rtpReceiver.on('error', (err) => {
    logRTP(`❌ CRITICAL: RTP Receiver error: ${err.message}`);
    console.error('RTP Receiver error details:', err);
    
    // Try to diagnose the error
    if (err.code === 'EADDRINUSE') {
      logRTP(`Port ${RTP_PORT} is already in use. Check: netstat -ulnp | grep ${RTP_PORT}`);
    } else if (err.code === 'EACCES') {
      logRTP(`Permission denied. Try running with sudo or use port > 1024`);
    } else if (err.code === 'ENOTFOUND') {
      logRTP(`Network interface not found`);
    }
  });

  // Enhanced message handler with detailed packet analysis
  rtpReceiver.on('message', (msg, rinfo) => {
    const now = Date.now();
    globalRtpStats.totalPackets++;
    globalRtpStats.totalBytes += msg.length;
    
    // Track unique sources
    const source = `${rinfo.address}:${rinfo.port}`;
    if (!globalRtpStats.sourcesDetected.has(source)) {
      globalRtpStats.sourcesDetected.add(source);
      logRTP(`🎉 NEW RTP SOURCE DETECTED: ${source} | Packet size: ${msg.length} bytes`);
      
      // Log first packet in detail
      logRTP(`First packet from ${source}:`);
      logRTP(`  - Raw bytes (first 20): ${msg.slice(0, 20).toString('hex')}`);
      logRTP(`  - Source address: ${rinfo.address} (expected from Asterisk)`);
      logRTP(`  - Source port: ${rinfo.port}`);
      logRTP(`  - Packet size: ${msg.length} bytes`);
    }
    
    // Log packet reception frequently initially
    const shouldLog = globalRtpStats.totalPackets <= 20 || 
                      globalRtpStats.totalPackets % 100 === 0 || 
                      !globalRtpStats.lastLogTime || 
                      now - globalRtpStats.lastLogTime >= 5000;
    
    if (shouldLog) {
      const duration = globalRtpStats.startTime ? (now - globalRtpStats.startTime) / 1000 : 0;
      const rate = duration > 0 ? (globalRtpStats.totalPackets / duration).toFixed(2) : '0';
      logRTP(`📦 Packet #${globalRtpStats.totalPackets} from ${source} | Size: ${msg.length}B | Rate: ${rate} pkt/s`);
      globalRtpStats.lastLogTime = now;
    }

    // Validate RTP packet structure
    if (msg.length < 12) {
      logRTP(`⚠️  WARNING: RTP packet too small (${msg.length} bytes) from ${source}`);
      logRTP(`  - Expected minimum: 12 bytes (RTP header)`);
      logRTP(`  - Received: ${msg.toString('hex')}`);
      return;
    }

    // Parse and log RTP header in detail
    const version = (msg[0] >> 6) & 0x3;
    const padding = (msg[0] >> 5) & 0x1;
    const extension = (msg[0] >> 4) & 0x1;
    const csrcCount = msg[0] & 0xF;
    const marker = (msg[1] >> 7) & 0x1;
    const payloadType = msg[1] & 0x7F;
    const sequenceNumber = msg.readUInt16BE(2);
    const timestamp = msg.readUInt32BE(4);
    const ssrc = msg.readUInt32BE(8);
    
    if (globalRtpStats.totalPackets <= 5) {
      logDebug(`🔍 RTP Header Analysis for packet #${globalRtpStats.totalPackets}:`);
      logDebug(`  - Version: ${version} (should be 2)`);
      logDebug(`  - Padding: ${padding}`);
      logDebug(`  - Extension: ${extension}`);
      logDebug(`  - CSRC Count: ${csrcCount}`);
      logDebug(`  - Marker: ${marker}`);
      logDebug(`  - Payload Type: ${payloadType} (0=μ-law, 8=A-law)`);
      logDebug(`  - Sequence: ${sequenceNumber}`);
      logDebug(`  - Timestamp: ${timestamp}`);
      logDebug(`  - SSRC: 0x${ssrc.toString(16)}`);
      logDebug(`  - Payload size: ${msg.length - 12} bytes`);
    }

    // Validate RTP version
    if (version !== 2) {
      logRTP(`⚠️  WARNING: Unexpected RTP version ${version} from ${source} (expected 2)`);
    }

    // Check payload type for audio formats
    if (payloadType !== 0 && payloadType !== 8) {
      logRTP(`⚠️  WARNING: Unexpected payload type ${payloadType} from ${source} (expected 0=μ-law or 8=A-law)`);
    }

    // Continue with existing packet processing...
    let channelId = [...sipMap.entries()].find(([_, data]) => 
      data.rtpSource && data.rtpSource.address === rinfo.address && data.rtpSource.port === rinfo.port
    )?.[0];
    
    if (!channelId) {
      // Try to assign RTP source to any channel waiting for it
      for (const [chId, chData] of sipMap.entries()) {
        if (!chData.rtpSource) {
          chData.rtpSource = rinfo;
          channelId = chId;
          logRTP(`🔗 RTP Source AUTO-ASSIGNED to channel ${chId}: ${source}`);
          logRTP(`  - Channel now has RTP source: ${chData.rtpSource.address}:${chData.rtpSource.port}`);
          break;
        }
      }
    }

    if (!channelId) {
      if (globalRtpStats.totalPackets <= 10) {
        logRTP(`⚠️  WARNING: RTP packet from ${source} has no matching channel`);
        logRTP(`  - Available channels: [${[...sipMap.keys()].join(', ')}]`);
        logRTP(`  - Channel count: ${sipMap.size}`);
        
        // Log channel details
        sipMap.forEach((data, id) => {
          logRTP(`  - Channel ${id}: RTP source = ${data.rtpSource ? data.rtpSource.address + ':' + data.rtpSource.port : 'none'}`);
        });
      }
      return;
    }

    // logDebug(`✅ Processing RTP packet for channel ${channelId} from ${source}`);

    // Continue with existing audio processing...
    
    // CRITICAL: ADD THIS AUDIO PROCESSING CODE
    // Extract audio payload from RTP packet
    const rtpHeaderSize = 12 + (csrcCount * 4); // Basic header + CSRC
    if (msg.length <= rtpHeaderSize) {
      logRTP(`⚠️ No audio payload in RTP packet from ${source}`);
      return;
    }
    
    const audioPayload = msg.slice(rtpHeaderSize); // Extract μ-law audio
    
    // Store raw audio from Asterisk
    if (!audioFromAsteriskMap.has(channelId)) {
      audioFromAsteriskMap.set(channelId, Buffer.alloc(0));
    }
    audioFromAsteriskMap.set(channelId, Buffer.concat([
      audioFromAsteriskMap.get(channelId), 
      audioPayload
    ]));
    
    // Buffer audio for batch processing
    if (!audioBuffers.has(channelId)) {
      audioBuffers.set(channelId, Buffer.alloc(0));
      
      // Set up interval to send buffered audio to OpenAI
      const intervalId = setInterval(() => {
        const buffer = audioBuffers.get(channelId);
        if (!buffer || buffer.length === 0) return;
        
        const channelData = sipMap.get(channelId);
        if (!channelData || !channelData.ws || channelData.ws.readyState !== WebSocket.OPEN) {
          clearInterval(intervalId);
          channelTimeouts.delete(channelId);
          audioBuffers.delete(channelId);
          return;
        }
        
        // Convert μ-law to 24kHz PCM
        const pcm24kHz = muLawToPcm24kHz(buffer, channelId);
        
        // Send to OpenAI
        if (pcm24kHz.length > 0) {
          const audioMessage = {
            type: 'input_audio_buffer.append',
            audio: pcm24kHz.toString('base64')
          };
          channelData.ws.send(JSON.stringify(audioMessage));
          // logClient(`Sent ${pcm24kHz.length}B audio to OpenAI for channel ${channelId}`);
        }
        
        // Clear buffer after sending
        audioBuffers.set(channelId, Buffer.alloc(0));
        
      }, BUFFER_INTERVAL_MS); // Send every 200ms
      
      channelTimeouts.set(channelId, intervalId);
    }
    
    // Append current audio to buffer
    audioBuffers.set(channelId, Buffer.concat([
      audioBuffers.get(channelId), 
      audioPayload
    ]));
    
    // Log progress periodically
    if (globalRtpStats.totalPackets % 50 === 0) {
      const bufferSize = audioBuffers.get(channelId)?.length || 0;
      logAudio(`Channel ${channelId} audio buffer: ${bufferSize}B`);
    }
  });

  // Try different bind configurations
  const bindConfigs = [
    { port: RTP_PORT, address: '0.0.0.0' },
    { port: RTP_PORT, address: '::' },
    { port: RTP_PORT }
  ];

  let bindSuccess = false;
  for (const config of bindConfigs) {
    try {
      rtpReceiver.bind(config);
      bindSuccess = true;
      logRTP(`✅ Successfully bound to ${JSON.stringify(config)}`);
      break;
    } catch (bindError) {
      logRTP(`❌ Failed to bind to ${JSON.stringify(config)}: ${bindError.message}`);
      if (config !== bindConfigs[bindConfigs.length - 1]) {
        logRTP(`Trying next bind configuration...`);
      }
    }
  }

  if (!bindSuccess) {
    logRTP(`❌ FATAL: Could not bind RTP receiver to any configuration`);
    throw new Error(`Failed to bind RTP receiver to port ${RTP_PORT}`);
  }
}

// Test RTP connectivity
async function testRTPConnectivity() {
  logRTP(`🧪 Testing RTP connectivity...`);
  
  // Test 1: Check if port is actually listening
  const { exec } = require('child_process');
  exec(`netstat -ulnp | grep :${RTP_PORT}`, (error, stdout, stderr) => {
    if (stdout) {
      logRTP(`✅ Port ${RTP_PORT} is listening: ${stdout.trim()}`);
    } else {
      logRTP(`❌ Port ${RTP_PORT} does not appear to be listening`);
    }
  });

  // Test 2: Send a test UDP packet to ourselves
  const testSocket = require('dgram').createSocket('udp4');
  const testMessage = Buffer.from('TEST_RTP_PACKET');
  
  testSocket.send(testMessage, RTP_PORT, 'localhost', (err) => {
    if (err) {
      logRTP(`❌ Self-test UDP send failed: ${err.message}`);
    } else {
      logRTP(`✅ Self-test UDP packet sent successfully`);
    }
    testSocket.close();
  });

  // Test 3: Check network interfaces
  const os = require('os');
  const interfaces = os.networkInterfaces();
  logRTP(`🌐 Available network interfaces:`);
  Object.keys(interfaces).forEach(name => {
    interfaces[name].forEach(iface => {
      if (!iface.internal && iface.family === 'IPv4') {
        logRTP(`  - ${name}: ${iface.address}`);
      }
    });
  });

  // Test 4: Log expected vs actual external IP
  logRTP(`🌍 Network configuration:`);
  logRTP(`  - Expected external IP: ${EXTERNAL_HOST_IP}`);
  logRTP(`  - RTP listening port: ${RTP_PORT}`);
  logRTP(`  - Asterisk should send RTP to: ${EXTERNAL_HOST_IP}:${RTP_PORT}`);
}

// Add periodic RTP diagnostics
setInterval(() => {
  const activeChannels = sipMap.size;
  const rtpSources = globalRtpStats.sourcesDetected.size;
  const totalRtpPackets = globalRtpStats.totalPackets;
  
  logRTP(`📊 RTP Status Report:`);
  logRTP(`  - Active channels: ${activeChannels}`);
  logRTP(`  - RTP sources detected: ${rtpSources}`);
  logRTP(`  - Total RTP packets received: ${totalRtpPackets}`);
  
  if (totalRtpPackets === 0 && activeChannels > 0) {
    logRTP(`⚠️  NO RTP PACKETS RECEIVED - Possible issues:`);
    logRTP(`  1. AWS Security Group not allowing UDP:${RTP_PORT} inbound`);
    logRTP(`  2. Asterisk cannot reach ${EXTERNAL_HOST_IP}:${RTP_PORT}`);
    logRTP(`  3. Firewall blocking UDP traffic`);
    logRTP(`  4. ExternalMedia channel configuration issue`);
    logRTP(`  5. Network routing problem`);
    
    logRTP(`🔧 Diagnostic commands to run:`);
    logRTP(`  - sudo tcpdump -i any port ${RTP_PORT}`);
    logRTP(`  - netstat -ulnp | grep ${RTP_PORT}`);
    logRTP(`  - nmap -sU -p ${RTP_PORT} ${EXTERNAL_HOST_IP}`);
  }
  
  // Log per-channel RTP status
  if (activeChannels > 0) {
    logRTP(`📞 Per-channel RTP status:`);
    sipMap.forEach((data, channelId) => {
      const rtpSource = data.rtpSource ? `${data.rtpSource.address}:${data.rtpSource.port}` : '❌ NONE';
      const wsState = data.ws ? `Connected (${data.ws.readyState})` : '❌ No WebSocket';
      logRTP(`  - Channel ${channelId}:`);
      logRTP(`    * RTP Source: ${rtpSource}`);
      logRTP(`    * WebSocket: ${wsState}`);
    });
  }
}, 15000); // Every 15 seconds
// Convert a single μ-law sample to 16-bit PCM
function muLawToPcm16(muLaw) {
  muLaw = ~muLaw & 0xFF; // Invert bits and mask
  const sign = (muLaw & 0x80) ? -1 : 1; // Extract sign
  const exponent = (muLaw & 0x70) >> 4; // Extract exponent
  const mantissa = muLaw & 0x0F; // Extract mantissa
  let sample = (exponent === 0) ? (mantissa * 8 + 16) : (1 << (exponent + 3)) * (mantissa + 16) - 128; // Decode sample
  sample = sign * sample;
  return Math.max(-32768, Math.min(32767, sample)); // Clamp to 16-bit range
}

// Convert μ-law buffer to 24kHz PCM with interpolation
function muLawToPcm24kHz(muLawBuffer, channelId) {
  if (!muLawBuffer || muLawBuffer.length === 0) {
    logAudio(`WARNING: Empty μ-law buffer for channel ${channelId}`);
    return Buffer.alloc(0);
  }

  const pcm8kHz = Buffer.alloc(muLawBuffer.length * 2); // Buffer for 8kHz PCM
  let maxSampleBefore = 0; // Track max sample before clamping
  let maxSampleAfter = 0; // Track max sample after clamping

  // Convert μ-law to 8kHz PCM
  for (let i = 0; i < muLawBuffer.length; i++) {
    let sample = muLawToPcm16(muLawBuffer[i]);
    maxSampleBefore = Math.max(maxSampleBefore, Math.abs(sample));
    sample = Math.round(sample);
    sample = Math.max(-32768, Math.min(32767, sample));
    maxSampleAfter = Math.max(maxSampleAfter, Math.abs(sample));
    pcm8kHz.writeInt16LE(sample, i * 2);
  }

  // Upsample to 24kHz with linear interpolation
  const pcm24kHz = Buffer.alloc(muLawBuffer.length * 3 * 2);
  let sumSquares = 0;
  for (let i = 0; i < muLawBuffer.length; i++) {
    const sample = pcm8kHz.readInt16LE(i * 2);
    const prevSample = i > 0 ? pcm8kHz.readInt16LE((i - 1) * 2) : sample;
    const nextSample = i < muLawBuffer.length - 1 ? pcm8kHz.readInt16LE((i + 1) * 2) : sample;
    const interp1 = Math.round((prevSample * 0.5 + sample * 0.5)); // First interpolated sample
    const interp2 = Math.round((sample * 0.75 + nextSample * 0.25)); // Second interpolated sample
    pcm24kHz.writeInt16LE(prevSample, (i * 3) * 2);
    pcm24kHz.writeInt16LE(interp1, (i * 3 + 1) * 2);
    pcm24kHz.writeInt16LE(interp2, (i * 3 + 2) * 2);
    sumSquares += prevSample * prevSample + interp1 * interp1 + interp2 * interp2; // For RMS calculation
  }

  const rms = Math.sqrt(sumSquares / (muLawBuffer.length * 3)) / 32768; // Calculate RMS
  
  // Store converted audio for OpenAI
  if (!audioToOpenAIMap.has(channelId)) audioToOpenAIMap.set(channelId, Buffer.alloc(0));
  audioToOpenAIMap.set(channelId, Buffer.concat([audioToOpenAIMap.get(channelId), pcm24kHz])); // Append to OpenAI buffer

  const now = Date.now();
  const logKey = `${channelId}_convert`;
  if (!amplificationLogFrequency.has(logKey) || now - amplificationLogFrequency.get(logKey) >= 3000) {
    logAudio(`Converted ${muLawBuffer.length}B μ-law -> ${pcm24kHz.length}B PCM24k for channel ${channelId} | RMS: ${rms.toFixed(3)} | Max: ${maxSampleBefore}->${maxSampleAfter}`);
    amplificationLogFrequency.set(logKey, now);
  }

  return pcm24kHz;
}

// Save PCM data as a WAV file
function saveWavFile(pcmData, filename, sampleRate) {
  const bitsPerSample = 16; // 16-bit audio
  const channels = 1; // Mono
  const byteRate = sampleRate * channels * (bitsPerSample / 8);
  const blockAlign = channels * (bitsPerSample / 8);
  const dataSize = pcmData.length;
  const fileSize = 36 + dataSize;

  const buffer = Buffer.alloc(44 + dataSize); // WAV header + data
  buffer.write('RIFF', 0);
  buffer.writeUInt32LE(fileSize, 4);
  buffer.write('WAVE', 8);
  buffer.write('fmt ', 12);
  buffer.writeUInt32LE(16, 16); // Subchunk size
  buffer.writeUInt16LE(1, 20); // PCM format
  buffer.writeUInt16LE(channels, 22);
  buffer.writeUInt32LE(sampleRate, 24);
  buffer.writeUInt32LE(byteRate, 28);
  buffer.writeUInt16LE(blockAlign, 32);
  buffer.writeUInt16LE(bitsPerSample, 34);
  buffer.write('data', 36);
  buffer.writeUInt32LE(dataSize, 40);
  pcmData.copy(buffer, 44); // Copy PCM data

  fs.writeFileSync(filename, buffer);
  logger.info(`Saved audio as ${filename}`);
}

// Save raw μ-law data to a file
function saveRawFile(data, filename) {
  fs.writeFileSync(filename, data);
  logger.info(`Saved raw μ-law as ${filename}`);
}

// Convert 16-bit PCM sample to μ-law
function pcm16ToMuLaw(sample) {
  const MAX = 32767;
  const MU = 255;
  const BIAS = 33;

  sample = Math.max(-MAX, Math.min(MAX, sample)); // Clamp to 16-bit range
  const sign = sample < 0 ? 0x80 : 0;
  let absSample = Math.abs(sample);

  if (absSample < 50) return 0x7F; // Silence threshold
  absSample += BIAS;

  const normalized = absSample / MAX;
  const muLaw = Math.log(1 + MU * normalized) / Math.log(1 + MU); // μ-law compression
  const quantized = Math.round(muLaw * 128);
  const exponent = Math.min(Math.floor(quantized / 16), 7);
  const mantissa = Math.min((quantized - (exponent * 16)), 15) & 0x0F;

  return ~(sign | (exponent << 4) | mantissa) & 0xFF; // Invert bits
}

// Resample 24kHz PCM to 8kHz
function resamplePcm24kHzTo8kHz(pcm24kHz) {
  const inSampleRate = 24000;
  const outSampleRate = 8000;
  const inSamples = pcm24kHz.length / 2;
  const outSamples = Math.floor(inSamples * outSampleRate / inSampleRate);
  const pcm8kHz = Buffer.alloc(outSamples * 2);

  for (let i = 0; i < outSamples; i++) {
    const srcPos = i * inSampleRate / outSampleRate;
    const srcIndex = Math.floor(srcPos);
    const frac = srcPos - srcIndex;

    if (srcIndex + 1 < inSamples) {
      const sample1 = pcm24kHz.readInt16LE(srcIndex * 2);
      const sample2 = pcm24kHz.readInt16LE((srcIndex + 1) * 2);
      const interpSample = Math.round(sample1 + frac * (sample2 - sample1)); // Linear interpolation
      pcm8kHz.writeInt16LE(interpSample, i * 2);
    } else if (srcIndex < inSamples) {
      pcm8kHz.writeInt16LE(pcm24kHz.readInt16LE(srcIndex * 2), i * 2);
    }
  }
  return pcm8kHz;
}

// Convert PCM buffer to μ-law, optionally resampling from 24kHz to 8kHz
function pcmToMuLaw(pcmBuffer, resample = false) {
  const input = resample ? resamplePcm24kHzTo8kHz(pcmBuffer) : pcmBuffer;
  const muLawBuffer = Buffer.alloc(input.length / 2);
  const chunkSize = 1024;
  for (let i = 0; i < input.length / 2; i += chunkSize) {
    const end = Math.min(i + chunkSize, input.length / 2);
    for (let j = i; j < end; j++) {
      let sample = input.readInt16LE(j * 2);
      sample = Math.max(-32767, Math.min(32767, Math.floor(sample * 0.95))); // Apply slight attenuation
      muLawBuffer[j] = pcm16ToMuLaw(sample);
    }
  }
  return muLawBuffer;
}

// Build RTP header for a packet
function buildRTPHeader(seq, timestamp, ssrc) {
  const header = Buffer.alloc(12);
  header[0] = 0x80; // Version 2, no padding, no extension
  header[1] = 0x00; // Payload type (0 for μ-law)
  header.writeUInt16BE(seq, 2); // Sequence number
  header.writeUInt32BE(timestamp, 4); // Timestamp
  header.writeUInt32BE(ssrc, 8); // Synchronization source
  return header;
}

// Async queue for sending RTP packets
const rtpQueue = async.queue((task, callback) => {
  rtpSender.send(task.packet, task.port, task.address, callback);
}, RTP_QUEUE_CONCURRENCY);

// Send an RTP packet with μ-law data
async function sendAudioPacket(muLawData, port, address, seq, timestamp, ssrc) {
  const startTime = process.hrtime.bigint();
  const header = buildRTPHeader(seq, timestamp, ssrc);
  const rtpPacket = Buffer.concat([header, muLawData]);
  await new Promise((resolve, reject) => {
    rtpQueue.push({ packet: rtpPacket, port, address }, (err) => {
      const elapsedMs = Number(process.hrtime.bigint() - startTime) / 1e6;
      if (ENABLE_RTP_LOGGING && seq % LOG_RTP_EVERY_N_PACKETS === 0) {
        logRTP(`Sent RTP packet seq=${seq}, timestamp=${timestamp}, elapsed=${elapsedMs.toFixed(2)}ms to ${address}:${port}`);
      }
      if (err) {
        logRTP(`ERROR: Failed to send RTP packet seq=${seq}: ${err.message}`);
        reject(err);
      } else {
        resolve();
      }
    });
  });
}

// Stream audio to Asterisk via RTP
const MAX_BUFFER_SIZE = 1024 * 1024; // Max buffer size (1MB)
async function streamAudio(channelId, rtpSource, initialBuffer = Buffer.alloc(0)) {
  const samplesPerPacket = 80; // 10 ms at 8000 Hz
  const packetIntervalNs = BigInt(10 * 1e6); // 10 ms in nanoseconds
  const { address, port } = rtpSource;

  logRTP(`Initializing RTP stream to ${address}:${port} for channel ${channelId}`);

  let rtpSequence = Math.floor(Math.random() * 65535); // Random initial sequence
  let rtpTimestamp = 0; // Initial timestamp
  const rtpSSRC = Math.floor(Math.random() * 4294967295); // Random SSRC
  let streamStartTime = process.hrtime.bigint();
  let isStreaming = true;
  let totalBytesSent = 0;
  let totalPacketsSent = 0;
  let stopRequested = false;
  let lastBufferSize = 0; // Previous buffer size
  let wasSending = false; // Track if we were sending data

  let muLawBuffer = Buffer.alloc(0); // Buffer for μ-law data
  let offset = 0; // Offset in buffer

  if (!rtpSentStats.has(channelId)) {
    rtpSentStats.set(channelId, { packets: 0, bytes: 0, startTime: null }); // Initialize stats
  }

  // Send a batch of RTP packets
  const sendPackets = async (data, packetCount, isSilence = false) => {
    let blockStartTime = process.hrtime.bigint();
    let nextPacketTime = blockStartTime;

    for (let i = 0; i < packetCount && !stopRequested; i++) {
      const bytesToSend = Math.min(samplesPerPacket, data.length - (i * samplesPerPacket));
      const packetData = data.slice(i * samplesPerPacket, i * samplesPerPacket + bytesToSend);
      const packetDataPadded = bytesToSend < samplesPerPacket ? Buffer.concat([packetData, Buffer.alloc(samplesPerPacket - bytesToSend, 0x7F)]) : packetData;

      await sendAudioPacket(packetDataPadded, port, address, rtpSequence, rtpTimestamp, rtpSSRC);
      if (i === 0 && !streamStartTime) streamStartTime = process.hrtime.bigint();
      rtpSequence = (rtpSequence + 1) % 65536;
      rtpTimestamp += 80;
      totalBytesSent += packetDataPadded.length;
      totalPacketsSent += 1;

      const stats = rtpSentStats.get(channelId);
      stats.packets += 1;
      stats.bytes += packetDataPadded.length;
      if (!stats.startTime) stats.startTime = Date.now();

      nextPacketTime += packetIntervalNs;
      const now = process.hrtime.bigint();
      if (now < nextPacketTime) {
        const delayMs = Number(nextPacketTime - now) / 1e6;
        await new Promise(resolve => setTimeout(resolve, delayMs)); // Maintain timing
      }
    }
    
    if (!isSilence && packetCount > 0) {
      logRTP(`Sent ${packetCount} audio packets for channel ${channelId} to ${address}:${port}`);
    }
  };

  const silencePacket = Buffer.alloc(samplesPerPacket, 0x7F); // Silence packet
  await sendPackets(silencePacket, 10, true); // Send initial silence
  logRTP(`RTP stream fully initialized for channel ${channelId} with ${10} silence packets`);

  // Process PCM chunks into μ-law
  const processFallback = async (pcmChunk) => {
    logDebug(`Processing ${pcmChunk.length}B PCM chunk for channel ${channelId}`);
    const muLawData = pcmToMuLaw(pcmChunk, true);
    muLawBuffer = Buffer.concat([muLawBuffer, muLawData]);
    if (muLawBuffer.length > MAX_BUFFER_SIZE) {
      const trimmed = muLawBuffer.length - MAX_BUFFER_SIZE;
      muLawBuffer = muLawBuffer.slice(trimmed); // Trim buffer
      logRTP(`Trimmed ${trimmed}B from RTP buffer for channel ${channelId} (was overflowing)`);
    }
    logAudio(`Added ${muLawData.length}B μ-law data to buffer for channel ${channelId} | Total buffer: ${muLawBuffer.length}B`);
  };

  // Main streaming loop
  const streamLoop = async () => {
    logRTP(`Starting RTP stream loop for channel ${channelId}`);
    while (isStreaming && !stopRequested) {
      if (!sipMap.has(channelId)) {
        logRTP(`Channel ${channelId} no longer active, stopping RTP stream`);
        break;
      }
      const currentBufferSize = muLawBuffer.length - offset;
      if (currentBufferSize >= samplesPerPacket) {
        const packetCount = Math.floor(currentBufferSize / samplesPerPacket);
        await sendPackets(muLawBuffer.slice(offset, offset + packetCount * samplesPerPacket), packetCount);
        offset += packetCount * samplesPerPacket;
        if (muLawBuffer.length - offset > MAX_BUFFER_SIZE / 2) {
          muLawBuffer = muLawBuffer.slice(offset);
          offset = 0; // Reset offset
        }
        wasSending = true;
      } else if (wasSending && currentBufferSize < samplesPerPacket) {
        logRTP(`RTP buffer to Asterisk fully sent for channel ${channelId} | Remaining: ${currentBufferSize} bytes`);
        wasSending = false;
      }
      lastBufferSize = currentBufferSize;
      await new Promise(resolve => setImmediate(resolve)); // Yield control
    }

    const totalDuration = Number(process.hrtime.bigint() - streamStartTime) / 1e9;
    logRTP(`Finished RTP stream for channel ${channelId} | Duration: ${totalDuration.toFixed(2)}s | Bytes: ${totalBytesSent} | Packets: ${totalPacketsSent}`);
    rtpSentStats.set(channelId, { packets: 0, bytes: 0, startTime: null }); // Reset stats
  };

  streamLoop();

  // Stop the stream
  const stop = async () => {
    logRTP(`Stopping RTP stream for channel ${channelId}`);
    isStreaming = false;
    stopRequested = true;
    muLawBuffer = Buffer.alloc(0);
    offset = 0;
    logRTP(`RTP stream stopped for channel ${channelId}`);
  };

  return {
    stop,
    write: processFallback, // Method to write PCM data
    muLawBuffer,
    offset
  };
}

// Start WebSocket connection to OpenAI real-time API
function startOpenAIWebSocket(channelId) {
  logClient(`Attempting to start OpenAI WebSocket for channel ${channelId}`);
  const ws = new WebSocket(REALTIME_URL, {
    headers: { 'Authorization': `Bearer ${OPENAI_API_KEY}`, 'OpenAI-Beta': 'realtime=v1' } // Authentication headers
  });

  let responseTimestamp = null; // Timestamp of response start
  let responseTranscript = ''; // Accumulated transcript
  let audioDeltaCount = 0; // Count of audio fragments
  let transcriptDeltaCount = 0; // Count of transcript fragments
  let audioReceivedLogged = false; // Flag for first audio log
  let audioSentTime = null; // Time audio was sent to OpenAI
  let callStartTime = null; // Call start time
  let maxCallTimeoutId = null; // Timeout ID for max call duration
  let totalPacketsSentThisResponse = 0; // Packets sent for current response
  let totalPacketsSentSession = 0; // Total packets sent in session
  let playbackComplete = false; // Playback completion flag
  let streamHandler = null; // RTP stream handler
  let isPlayingResponse = false; // Flag for active response playback

  // Initialize RTP stream handler
  const initializeStreamHandler = async () => {
    const channelData = sipMap.get(channelId);
    if (channelData && channelData.rtpSource) {
      logClient(`Initializing StreamHandler for channel ${channelId} with RTP source ${channelData.rtpSource.address}:${channelData.rtpSource.port}`);
      streamHandler = await streamAudio(channelId, channelData.rtpSource);
      logClient(`StreamHandler initialized for channel ${channelId} | Ready: ${streamHandler !== null}`);
    } else {
      logClient(`Cannot initialize StreamHandler: No RTP source for channel ${channelId} | Channel data: ${channelData ? 'exists' : 'missing'}`);
    }
    return streamHandler;
  };

  // WebSocket open event
  ws.on('open', async () => {
    callStartTime = Date.now();
    logClient(`OpenAI WebSocket connection established for channel ${channelId}`);
    
    // Send session configuration
    const sessionConfig = {
      type: 'session.update',
      session: {
        modalities: ['audio', 'text'], // Enable audio and text responses
        voice: 'alloy', // Voice for OpenAI responses
        instructions: get_org_instruct('Nayatel'), // Use organization instructions
        turn_detection: {
          type: 'server_vad', // Server-side Voice Activity Detection
          threshold: VAD_THRESHOLD,
          prefix_padding_ms: VAD_PREFIX_PADDING_MS,
          silence_duration_ms: VAD_SILENCE_DURATION_MS
        },
        input_audio_transcription: { model: 'whisper-1' }, // Transcription model
        tools: getDefaultTools() // Add default tools
      }
    };
    
    ws.send(JSON.stringify(sessionConfig));
    logClient(`Session configured for channel ${channelId} | VAD: ${VAD_THRESHOLD} | Padding: ${VAD_PREFIX_PADDING_MS}ms | Silence: ${VAD_SILENCE_DURATION_MS}ms`);

    // Set max call duration timeout
    maxCallTimeoutId = setTimeout(async () => {
      logClient(`Max call duration (${MAX_CALL_DURATION}ms) reached for channel ${channelId}, closing connection and hanging up`);
      ws.close();
      const channelData = sipMap.get(channelId);
      if (channelData && channelData.bridge) {
        try {
          await ariClient.channels.hangup({ channelId: channelId });
          logger.info(`Channel ${channelId} hung up due to max call duration`);
        } catch (err) {
          logger.error(`Failed to hang up channel ${channelId}: ${err.message}`);
        }
      }
    }, MAX_CALL_DURATION);
  });

  // Handle incoming WebSocket messages from OpenAI
  ws.on('message', async (data) => {
    try {
      const response = JSON.parse(data.toString());
      receivedEventCounter++;
      const duration = audioSentTime ? ((Date.now() - audioSentTime) / 1000).toFixed(2) : 'N/A';

      if (receivedEventCounter === 0) {
        logServer(`First event received for channel ${channelId} | Type: ${response.type} | Duration: ${duration}s`);
      }

      switch (response.type) {
        case 'session.created':
          logServer(`Session created for channel ${channelId} | Duration: ${duration}s`);
          break;
          
        case 'session.updated':
          logServer(`Session updated for channel ${channelId} | Duration: ${duration}s`);
          break;
          
        case 'input_audio_buffer.speech_started':
          logServer(`Speech started detected for channel ${channelId} | Duration: ${duration}s`);
          break;
          
        case 'input_audio_buffer.speech_stopped':
          logServer(`Speech stopped detected for channel ${channelId} | Duration: ${duration}s`);
          audioSentTime = Date.now();
          if (streamHandler) {
            await streamHandler.stop();
            logClient(`Stopped RTP stream due to user speech for channel ${channelId}`);
            streamHandler = null;
          }
          break;
          
        case 'conversation.item.input_audio_transcription.completed':
          logServer(`User speech transcribed for channel ${channelId} | Text: "${response.transcript.trim()}" | Duration: ${duration}s`);
          break;
          
        case 'response.audio.delta':
          audioDeltaCount++;
          if (!audioReceivedLogged) {
            responseTimestamp = Date.now();
            logServer(`Audio response started for channel ${channelId} | Duration: ${duration}s`);
            audioReceivedLogged = true;
          }
          isPlayingResponse = true;
          
          try {
            const pcmChunk = Buffer.from(response.delta, 'base64'); // Decode audio chunk
            logServer(`Audio delta #${audioDeltaCount} for channel ${channelId} | Size: ${(pcmChunk.length / 1024).toFixed(2)}KB`);
            
            // Initialize StreamHandler for response audio if needed
            const channelData = sipMap.get(channelId);
            if (!streamHandler && channelData && channelData.rtpSource) {
              logClient(`Initializing StreamHandler for response audio on channel ${channelId}`);
              streamHandler = await streamAudio(channelId, channelData.rtpSource);
              logClient(`StreamHandler ready for response playback on channel ${channelId}`);
            }
            
            if (streamHandler) {
              await streamHandler.write(pcmChunk); // Send to Asterisk
              totalPacketsSentThisResponse += Math.ceil(pcmChunk.length / 160);
              totalPacketsSentSession += Math.ceil(pcmChunk.length / 160);
              logAudio(`Wrote ${pcmChunk.length}B to RTP stream for channel ${channelId}`);
            } else {
              logClient(`ERROR: No StreamHandler available for audio playback on channel ${channelId} | RTP source: ${channelData?.rtpSource ? 'available' : 'missing'}`);
            }
          } catch (audioError) {
            logServer(`ERROR: Failed to process audio delta for channel ${channelId}: ${audioError.message}`);
          }
          break;
          
        case 'response.audio_transcript.delta':
          transcriptDeltaCount++;
          responseTranscript += response.delta; // Accumulate transcript
          break;
          
        case 'response.audio_transcript.done':
          logServer(`Response transcript completed for channel ${channelId} | Text: "${response.transcript.trim()}" | Duration: ${duration}s`);
          responseTranscript = '';
          break;
          
        case 'response.done':
          audioReceivedLogged = false;
          isPlayingResponse = false;
          const stats = rtpSentStats.get(channelId) || { packets: 0, bytes: 0, startTime: responseTimestamp };
          const responseDuration = responseTimestamp ? ((Date.now() - responseTimestamp) / 1000).toFixed(2) : 'N/A';
          logServer(`Response completed for channel ${channelId} | Duration: ${responseDuration}s | Audio deltas: ${audioDeltaCount} | Text deltas: ${transcriptDeltaCount} | RTP packets: ${stats.packets} | RTP bytes: ${stats.bytes}`);
          audioDeltaCount = 0;
          transcriptDeltaCount = 0;
          totalPacketsSentThisResponse = 0;
          
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({ type: 'input_audio_buffer.clear' })); // Clear OpenAI buffer
            logClient(`Cleared OpenAI audio buffer for channel ${channelId}`);
          }
          break;
          
        case 'error':
          logServer(`ERROR from OpenAI for channel ${channelId} | Message: ${response.error.message} | Code: ${response.error.code || 'N/A'}`);
          if (streamHandler) {
            await streamHandler.stop();
            streamHandler = null;
          }
          break;
          
        default:
          logServer(`Unknown event type for channel ${channelId} | Type: ${response.type}`);
      }
    } catch (parseError) {
      logServer(`ERROR: Failed to parse WebSocket message for channel ${channelId}: ${parseError.message}`);
    }
  });

  ws.on('error', (error) => {
    logClient(`OpenAI WebSocket error for channel ${channelId} | Message: ${error.message}`);
    if (streamHandler) {
      streamHandler.stop();
      streamHandler = null;
    }
  });

  ws.on('close', () => {
    if (maxCallTimeoutId) clearTimeout(maxCallTimeoutId);
    if (streamHandler) {
      streamHandler.stop();
      streamHandler = null;
    }
    logClient(`OpenAI WebSocket connection closed for channel ${channelId}`);
  });

  return { 
    ws, 
    getPlaybackComplete: () => playbackComplete, 
    stopStream: () => streamHandler && streamHandler.stop(),
    initializeStreamHandler
  };
}

// Main async function to initialize ARI and handle events
(async () => {
  try {
    logger.info(`Starting Asterisk to OpenAI Real-time Bridge`);
    logger.info(`Configuration | ARI: ${ARI_HOST}:${ARI_PORT} | App: ${ARI_APP} | RTP Port: ${RTP_PORT}`);
    logger.info(`Audio Settings | VAD Threshold: ${VAD_THRESHOLD} | Prefix: ${VAD_PREFIX_PADDING_MS}ms | Silence: ${VAD_SILENCE_DURATION_MS}ms`);
    
    // 1. INITIALIZE THE NEW CLIENT
    ariClient = new AriClient({
      host: ARI_HOST,
      port: ARI_PORT,
      username: ARI_USER,
      password: ARI_PASS,
      secure: process.env.ARI_SSL_ENABLED,
      rejectUnauthorized: false
    });
    logger.info(`ARI Client initialized for API calls to ${ARI_HOST}:${ARI_PORT}/ari`);

    // 2. CONNECT THE WEBSOCKET **FIRST**
    await ariClient.connectWebSocket([ARI_APP]);
    logger.info('✅ Success! ARI WebSocket connection established.');

    // 3. SET UP EVENT HANDLERS

    // Handle new channel entering Stasis
    ariClient.on('StasisStart', async (event) => {
        const { channel } = event;
        logger.info(`StasisStart event received for channel ${channel.id}, name: ${channel.name}`);

        if (channel.name && channel.name.startsWith('UnicastRTP')) {
          logger.info(`ExternalMedia channel started: ${channel.id}`);
          let mapping = extMap.get(channel.id);
          if (!mapping) {
            logDebug(`Waiting for mapping for ExternalMedia channel ${channel.id}`);
            await new Promise(r => setTimeout(r, 500));
            mapping = extMap.get(channel.id);
          }
          if (mapping) {
            try {
              await ariClient.bridges.addChannels(mapping.bridgeId, {
                channel: [channel.id]
              });
              logger.info(`ExternalMedia channel ${channel.id} added to bridge ${mapping.bridgeId}`);
              
              // Set up RTP source detection for the mapped SIP channel
              const channelData = sipMap.get(mapping.channelId);
              if (channelData && !channelData.rtpSource) {
                logDebug(`Setting up RTP source detection for channel ${mapping.channelId}`);
                // Don't wait for specific RTP - let the receiver handle assignment
                logger.info(`RTP source detection prepared for channel ${mapping.channelId}`);
              }
            } catch (err) {
              logger.error(`Failed to add ExternalMedia channel ${channel.id} to bridge: ${err.message}`);
            }
          } else {
            logger.error(`No mapping found for ExternalMedia channel ${channel.id}`);
          }
          return;
        }

        logger.info(`SIP channel started: ${channel.id}`);
        try {
          // Create bridge using the correct method for @ipcom/asterisk-ari
          logDebug(`Creating bridge for channel ${channel.id}`);
          const bridgeResponse = await ariClient.bridges.createBridge({
            type: 'mixing,proxy_media',
            name: `bridge-${channel.id}`
          });
          const bridgeId = bridgeResponse.id;
          logger.info(`Bridge created for channel ${channel.id} | Bridge ID: ${bridgeId}`);
          
          // Add channel to bridge using correct method
          logDebug(`Adding SIP channel ${channel.id} to bridge ${bridgeId}`);
          await ariClient.bridges.addChannels(bridgeId, {
            channel: [channel.id]
          });
          logger.info(`SIP channel ${channel.id} added to bridge ${bridgeId}`);
          
          // Answer channel using the instance method
          logDebug(`Answering channel ${channel.id}`);
          const channelInstance = ariClient.Channel(channel.id);
          await channelInstance.answer();
          logger.info(`Channel ${channel.id} answered`);

          // Create ExternalMedia channel
          const extParams = {
            app: ARI_APP,
            external_host: `${EXTERNAL_HOST_IP}:${RTP_PORT}`,
            format: 'ulaw',
            transport: 'udp',
            encapsulation: 'rtp',
            connection_type: 'client',
            direction: 'both'
          };
          logDebug(`Creating ExternalMedia channel for ${channel.id} | External host: ${EXTERNAL_HOST_IP}:${RTP_PORT}`);
          const extChannel = await ariClient.channels.createExternalMedia(extParams);
          extMap.set(extChannel.id, { bridgeId: bridgeId, channelId: channel.id });
          logger.info(`ExternalMedia channel ${extChannel.id} created and mapped to bridge ${bridgeId} for SIP channel ${channel.id}`);

          // Start OpenAI WebSocket
          const openaiConnection = startOpenAIWebSocket(channel.id);
          sipMap.set(channel.id, { 
            bridge: { id: bridgeId }, 
            ws: openaiConnection.ws,
            channelId: channel.id, 
            sendTimeout: null, 
            rtpSource: null, // Will be assigned when RTP packets arrive
            getPlaybackComplete: openaiConnection.getPlaybackComplete,
            stopStream: openaiConnection.stopStream,
            initializeStreamHandler: openaiConnection.initializeStreamHandler
          });
          
          logger.info(`Channel ${channel.id} fully initialized | Bridge: ${bridgeId} | OpenAI: Connected | RTP: Awaiting packets`);

        } catch (e) {
          logger.error(`Error setting up SIP channel ${channel.id}: ${e.message}`);
          console.error('Full error details:', e);
        }
    });

    // Handle channel leaving Stasis (call end)
    ariClient.on('StasisEnd', async (event) => {
      const { channel } = event;
      logger.info(`StasisEnd event received for channel ${channel.id}, name: ${channel.name || 'unknown'}`);

      if (channel.name && channel.name.startsWith('UnicastRTP')) {
        const mapping = extMap.get(channel.id);
        if (mapping) {
          logDebug(`Removing ExternalMedia mapping for channel ${channel.id}`);
        }
        extMap.delete(channel.id);
        logger.info(`ExternalMedia channel ${channel.id} removed from map`);
      } else {
        const channelData = sipMap.get(channel.id);
        if (channelData) {
          try {
            logger.info(`Starting cleanup for SIP channel ${channel.id}`);
            sipMap.delete(channel.id);
            logger.info(`Channel ${channel.id} removed from sipMap`);

            // Clear audio send timeout
            if (channelData.sendTimeout) {
              clearInterval(channelData.sendTimeout);
              channelData.sendTimeout = null;
              logDebug(`Send timeout cleared for channel ${channel.id}`);
            }

            // Stop RTP stream
            if (channelData.stopStream) {
              await channelData.stopStream();
              logDebug(`StreamHandler stopped for channel ${channel.id}`);
            }

            // Brief wait if playback incomplete
            if (!channelData.getPlaybackComplete()) {
              logDebug(`Waiting briefly for playback completion on channel ${channel.id}`);
              await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Close WebSocket
            if (channelData.ws && channelData.ws.readyState === WebSocket.OPEN) {
              channelData.ws.close();
              logDebug(`WebSocket closed for channel ${channel.id}`);
            }

            // Destroy bridge - FIXED the method call
            if (channelData.bridge && channelData.bridge.id) {
              try {
                await ariClient.bridges.destroyBridge({ bridgeId: channelData.bridge.id });
                logDebug(`Bridge ${channelData.bridge.id} destroyed for channel ${channel.id}`);
              } catch (bridgeError) {
                logger.error(`Failed to destroy bridge ${channelData.bridge.id}: ${bridgeError.message}`);
              }
            }
            
            logger.info(`Cleanup completed for channel ${channel.id}`);
          } catch (e) {
            logger.error(`Error during cleanup for channel ${channel.id}: ${e.message}`);
          }
        }

        // Save audio files if enabled
        if (ENABLE_SENT_TO_OPENAI_RECORDING) {
          if (audioFromAsteriskMap.has(channel.id) && audioFromAsteriskMap.get(channel.id).length > 0) {
            saveRawFile(audioFromAsteriskMap.get(channel.id), `asterisk_input_mulaw_raw_${channel.id}.raw`);
            audioFromAsteriskMap.delete(channel.id);
          }
          if (audioToOpenAIMap.has(channel.id) && audioToOpenAIMap.get(channel.id).length > 0) {
            saveWavFile(audioToOpenAIMap.get(channel.id), `sent_to_openai_${channel.id}.wav`, 24000);
            audioToOpenAIMap.delete(channel.id);
          }
        }
        
        // Clean up frequency tracking maps
        amplificationLogFrequency.delete(channel.id);
        rmsLogFrequency.delete(channel.id);
        rtpSentStats.delete(channel.id);
      }
    });

    ariClient.on('error', (err) => logger.error(`ARI client error: ${err.message}`));
    ariClient.on('close', () => logger.info('ARI WebSocket connection closed'));
    
    // 4. SET UP AND BIND THE RTP RECEIVER
    startRTPReceiver();
    
    logger.info(`🚀 Application ready and listening for events in "${ARI_APP}"...`);
    logger.info(`📞 Ready to handle calls | RTP listening on 0.0.0.0:${RTP_PORT}`);

  } catch (err) {
    logger.error(`ARI connection error: ${err.message}`);
    console.error('Full ARI connection error:', err);
    process.exit(1); // Exit on connection failure
  }
})();

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error(`Uncaught Exception: ${err.message}`);
  console.error('Uncaught Exception details:', err);
  cleanup();
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error(`Unhandled Rejection at: ${promise} | Reason: ${reason}`);
  console.error('Unhandled Rejection details:', reason);
});

// Handle SIGINT (Ctrl+C)
process.on('SIGINT', () => {
  logger.info('Received SIGINT, cleaning up...');
  cleanup();
  process.exit(0);
});

// Handle SIGTERM
process.on('SIGTERM', () => {
  logger.info('Received SIGTERM, cleaning up...');
  cleanup();
  process.exit(0);
});

// Cleanup function to close sockets and connections
function cleanup() {
  logger.info('Starting cleanup process...');
  
  sipMap.forEach((data, channelId) => {
    logger.info(`Cleaning up channel ${channelId}`);
    if (data.ws) {
      data.ws.close();
      logDebug(`WebSocket closed for channel ${channelId} during cleanup`);
    }
    if (data.sendTimeout) {
      clearInterval(data.sendTimeout);
      logDebug(`Send timeout cleared for channel ${channelId} during cleanup`);
    }
    if (data.stopStream) {
      data.stopStream();
      logDebug(`Stream stopped for channel ${channelId} during cleanup`);
    }
  });
  
  if (rtpSender) {
    rtpSender.close();
    logRTP('RTP sender socket closed');
  }
  
  if (rtpReceiver) {
    rtpReceiver.close();
    logRTP('RTP receiver socket closed');
  }
  
  // Clear maps
  sipMap.clear();
  extMap.clear();
  audioFromAsteriskMap.clear();
  audioToOpenAIMap.clear();
  amplificationLogFrequency.clear();
  rmsLogFrequency.clear();
  rtpSentStats.clear();
  
  logger.info('Cleanup completed');
}

// Periodic status reporting
setInterval(() => {
  const activeChannels = sipMap.size;
  const rtpSources = globalRtpStats.sourcesDetected.size;
  const totalRtpPackets = globalRtpStats.totalPackets;
  
  if (activeChannels > 0 || totalRtpPackets > 0) {
    logger.info(`📊 Status | Active channels: ${activeChannels} | RTP sources: ${rtpSources} | Total RTP packets: ${totalRtpPackets}`);
    
    // Log per-channel status
    sipMap.forEach((data, channelId) => {
      const rtpSource = data.rtpSource ? `${data.rtpSource.address}:${data.rtpSource.port}` : 'none';
      const wsState = data.ws ? data.ws.readyState : 'no-ws';
      const hasTimeout = data.sendTimeout ? 'yes' : 'no';
      logDebug(`Channel ${channelId} | RTP: ${rtpSource} | WS: ${wsState} | Timeout: ${hasTimeout}`);
    });
  }
}, 30000); // Every 30 seconds

// Export for testing (optional)
module.exports = {
  startRTPReceiver,
  startOpenAIWebSocket,
  cleanup,
  muLawToPcm16,
  pcmToMuLaw
};