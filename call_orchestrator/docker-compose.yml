version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  call-orchestrator:
    build: .
    ports:
      - "8000:8000"
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials.json
      - PROJECT_ID=${PROJECT_ID}
      - SUBSCRIPTION_NAME=${SUBSCRIPTION_NAME}
      - REDIS_URL=redis://redis:6379
      - OUTBOUND_CALLER_SERVICE_URL=${OUTBOUND_CALLER_SERVICE_URL}
      - WEBHOOK_BASE_URL=${WEBHOOK_BASE_URL}
    volumes:
      - ./credentials.json:/app/credentials.json:ro
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped

volumes:
  redis_data: