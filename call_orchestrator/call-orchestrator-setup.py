"""
Setup script for Call Queue Orchestrator
Creates Pub/Sub topics and subscriptions
"""

import os
import json
from google.cloud import pubsub_v1
from google.api_core.exceptions import AlreadyExists

def setup_pubsub():
    """Create Pub/Sub topic and subscription"""
    
    project_id = os.getenv("PROJECT_ID", "your-project-id")
    topic_name = "call-queue"
    subscription_name = "call-queue-subscription"
    
    publisher = pubsub_v1.PublisherClient()
    subscriber = pubsub_v1.SubscriberClient()
    
    # Create topic
    topic_path = publisher.topic_path(project_id, topic_name)
    try:
        topic = publisher.create_topic(request={"name": topic_path})
        print(f"Created topic: {topic.name}")
    except AlreadyExists:
        print(f"Topic {topic_path} already exists")
    
    # Create subscription with settings
    subscription_path = subscriber.subscription_path(project_id, subscription_name)
    
    try:
        subscription = subscriber.create_subscription(
            request={
                "name": subscription_path,
                "topic": topic_path,
                "ack_deadline_seconds": 60,  # 60 seconds to process
                "enable_exactly_once_delivery": True,  # Prevent duplicates
                "retry_policy": {
                    "minimum_backoff": {"seconds": 10},
                    "maximum_backoff": {"seconds": 600}
                },
                "dead_letter_policy": {
                    "dead_letter_topic": publisher.topic_path(project_id, "call-queue-dlq"),
                    "max_delivery_attempts": 5
                }
            }
        )
        print(f"Created subscription: {subscription.name}")
    except AlreadyExists:
        print(f"Subscription {subscription_path} already exists")
    
    return topic_path, subscription_path


def publish_test_messages(topic_path, count=5):
    """Publish test messages to the queue"""
    
    publisher = pubsub_v1.PublisherClient()
    
    test_calls = [
        {
            "from_number": "+12314987814",
            "to_number": f"+92303444582{i}",
            "mission": "Lead generation",
            "organisation_id": "jazzcash",
            "campaign_id": "EFU Sales",
            "booking_id": 34 + i,
            "customer_name": f"Test Customer {i}"
        }
        for i in range(count)
    ]
    
    futures = []
    for call_data in test_calls:
        data = json.dumps(call_data).encode("utf-8")
        future = publisher.publish(topic_path, data)
        futures.append(future)
        print(f"Published message for {call_data['to_number']}")
    
    # Wait for all publishes to complete
    for future in futures:
        future.result()
    
    print(f"Published {count} test messages")


# Monitoring Dashboard Configuration (Grafana)
GRAFANA_DASHBOARD = {
    "dashboard": {
        "title": "Call Queue Orchestrator",
        "panels": [
            {
                "title": "Active Calls",
                "type": "graph",
                "targets": [
                    {
                        "expr": "redis_key_count{key='active_calls'}"
                    }
                ]
            },
            {
                "title": "Call Processing Rate",
                "type": "graph",
                "targets": [
                    {
                        "expr": "rate(calls_processed_total[5m])"
                    }
                ]
            },
            {
                "title": "Call Success Rate",
                "type": "stat",
                "targets": [
                    {
                        "expr": "sum(rate(calls_completed_total[5m])) / sum(rate(calls_processed_total[5m])) * 100"
                    }
                ]
            },
            {
                "title": "Queue Depth",
                "type": "graph",
                "targets": [
                    {
                        "expr": "pubsub_subscription_backlog{subscription='call-queue-subscription'}"
                    }
                ]
            },
            {
                "title": "Average Call Duration",
                "type": "graph",
                "targets": [
                    {
                        "expr": "rate(call_duration_seconds_sum[5m]) / rate(call_duration_seconds_count[5m])"
                    }
                ]
            },
            {
                "title": "Error Rate",
                "type": "graph",
                "targets": [
                    {
                        "expr": "rate(calls_failed_total[5m])"
                    }
                ]
            }
        ]
    }
}


# Prometheus metrics endpoint (add to main service)
from prometheus_client import Counter, Gauge, Histogram, generate_latest

# Metrics
active_calls_gauge = Gauge('active_calls', 'Number of active calls')
calls_processed_total = Counter('calls_processed_total', 'Total calls processed')
calls_completed_total = Counter('calls_completed_total', 'Total calls completed', ['status'])
calls_failed_total = Counter('calls_failed_total', 'Total calls failed')
call_duration_seconds = Histogram('call_duration_seconds', 'Call duration in seconds')
queue_depth_gauge = Gauge('queue_depth', 'Number of messages in queue')


# Add this endpoint to your FastAPI app
async def metrics_endpoint():
    """Prometheus metrics endpoint"""
    # Update metrics
    status = await orchestrator.get_status()
    active_calls_gauge.set(status['active_calls'])
    
    # Generate metrics
    return generate_latest()


# Load testing script
import asyncio
import aiohttp
import time
from typing import List

class LoadTester:
    """Load test the call orchestrator"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.results = []
    
    async def publish_calls_to_queue(self, count: int, rate_per_second: int):
        """Publish calls to the queue at a specified rate"""
        
        publisher = pubsub_v1.PublisherClient()
        topic_path = publisher.topic_path(
            os.getenv("PROJECT_ID"),
            "call-queue"
        )
        
        delay = 1.0 / rate_per_second
        
        for i in range(count):
            call_data = {
                "from_number": "+12314987814",
                "to_number": f"+92303{str(i).zfill(7)}",
                "mission": "Load test",
                "organisation_id": "test_org",
                "campaign_id": "load_test",
                "booking_id": 1000 + i,
                "customer_name": f"Load Test {i}"
            }
            
            data = json.dumps(call_data).encode("utf-8")
            publisher.publish(topic_path, data)
            
            if i % 10 == 0:
                print(f"Published {i}/{count} calls")
            
            await asyncio.sleep(delay)
    
    async def monitor_system(self, duration_seconds: int):
        """Monitor system metrics during load test"""
        
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            
            while time.time() - start_time < duration_seconds:
                try:
                    async with session.get(f"{self.base_url}/status") as resp:
                        if resp.status == 200:
                            status = await resp.json()
                            self.results.append({
                                "timestamp": time.time(),
                                "active_calls": status["active_calls"],
                                "available_slots": status["available_slots"]
                            })
                            print(f"Active: {status['active_calls']}/{status['max_concurrent']}")
                except Exception as e:
                    print(f"Monitor error: {e}")
                
                await asyncio.sleep(1)
    
    def generate_report(self):
        """Generate load test report"""
        
        if not self.results:
            print("No results collected")
            return
        
        max_concurrent = max(r["active_calls"] for r in self.results)
        avg_concurrent = sum(r["active_calls"] for r in self.results) / len(self.results)
        
        print("\n=== Load Test Report ===")
        print(f"Duration: {len(self.results)} seconds")
        print(f"Max Concurrent Calls: {max_concurrent}")
        print(f"Average Concurrent Calls: {avg_concurrent:.2f}")
        print(f"Min Available Slots: {min(r['available_slots'] for r in self.results)}")
        
        # Check if system maintained concurrency limit
        if max_concurrent <= 30:
            print("✅ Concurrency limit maintained")
        else:
            print("❌ Concurrency limit exceeded!")


async def run_load_test():
    """Run a load test"""
    
    tester = LoadTester()
    
    # Start monitoring
    monitor_task = asyncio.create_task(tester.monitor_system(120))  # 2 minutes
    
    # Publish calls at 10 per second
    await tester.publish_calls_to_queue(count=500, rate_per_second=10)
    
    # Wait for monitoring to complete
    await monitor_task
    
    # Generate report
    tester.generate_report()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "setup":
            topic_path, subscription_path = setup_pubsub()
            print(f"\nSetup complete!")
            print(f"Topic: {topic_path}")
            print(f"Subscription: {subscription_path}")
            
        elif command == "test":
            topic_path, _ = setup_pubsub()
            publish_test_messages(topic_path, count=10)
            
        elif command == "load_test":
            asyncio.run(run_load_test())
            
        else:
            print(f"Unknown command: {command}")
            print("Usage: python setup.py [setup|test|load_test]")
    else:
        print("Usage: python setup.py [setup|test|load_test]")