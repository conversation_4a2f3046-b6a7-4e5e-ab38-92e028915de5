#!/usr/bin/env python3
"""
Test script to verify Redis Cloud connection (non-SSL)
"""
import asyncio
import redis.asyncio as redis
import os
from dotenv import load_dotenv
load_dotenv()

async def test_redis_connection():
    """Test Redis Cloud connection without SSL"""
    
    # Your Redis Cloud credentials (working configuration)
    redis_host = os.getenv("redis_host")
    redis_port = os.getenv("redis_port")
    redis_password = os.getenv("redis_password")
    redis_username = os.getenv("redis_username")
    
    print(f"Testing connection to Redis Cloud...")
    print(f"Host: {redis_host}:{redis_port}")
    print(f"Username: {redis_username}")
    print(f"SSL: Disabled (as confirmed by redis-cli test)")
    print()
    
    try:
        # Use non-SSL connection (redis:// not rediss://)
        redis_url = f"redis://{redis_username}:{redis_password}@{redis_host}:{redis_port}"
        
        client = redis.from_url(
            redis_url,
            encoding="utf-8",
            decode_responses=True,
            socket_timeout=10,
            socket_connect_timeout=10,
            max_connections=10
        )
        
        print("Testing PING...")
        pong = await client.ping()
        print(f"✅ PING response: {pong}")
        
        print("\nTesting SET/GET...")
        await client.set("test_key", "test_value", ex=60)
        value = await client.get("test_key")
        print(f"✅ SET/GET test: {value}")
        
        print("\nTesting stats operations...")
        await client.set("stats:total_processed", 0)
        await client.incr("stats:total_processed")
        stats = await client.get("stats:total_processed")
        print(f"✅ Stats test: {stats}")
        
        print("\nTesting hash operations (used by orchestrator)...")
        test_call_id = "test_call_123"
        call_data = {
            "call_id": test_call_id,
            "status": "pending",
            "created_at": "2024-01-01T00:00:00",
            "from_number": "+1234567890",
            "to_number": "+0987654321"
        }
        
        await client.hset(f"call:{test_call_id}:data", mapping=call_data)
        retrieved_data = await client.hgetall(f"call:{test_call_id}:data")
        print(f"✅ Hash operations test: {len(retrieved_data)} fields retrieved")
        
        print("\nTesting list operations (used for callbacks)...")
        await client.rpush(f"call:{test_call_id}:callbacks", '{"status": "test"}')
        callbacks = await client.lrange(f"call:{test_call_id}:callbacks", 0, -1)
        print(f"✅ List operations test: {len(callbacks)} callbacks")
        
        # Cleanup test data
        await client.delete("test_key", "stats:total_processed", 
                          f"call:{test_call_id}:data", f"call:{test_call_id}:callbacks")
        
        await client.close()
        
        print("\n🎉 SUCCESS: All Redis Cloud operations working perfectly!")
        print("✅ Your orchestrator should connect successfully")
        return True
        
    except Exception as e:
        print(f"\n❌ FAILED: {e}")
        print("\n🔍 If you see this error, check:")
        print("1. Network connectivity to Redis Cloud")
        print("2. Redis Cloud instance is running") 
        print("3. Password hasn't changed")
        return False

if __name__ == "__main__":
    print("Redis Cloud Connection Test (Non-SSL)")
    print("=" * 50)
    
    success = asyncio.run(test_redis_connection())
    
    if success:
        print("\n🚀 Ready for production deployment!")
        print("Your orchestrator code will use:")
        print("  - redis:// (no SSL)")
        print("  - Host: redis-17511.fcrce172.us-east-1-1.ec2.redns.redis-cloud.com")
        print("  - Port: 17511")
        print("  - Auth: default user with password")
    
    exit(0 if success else 1)