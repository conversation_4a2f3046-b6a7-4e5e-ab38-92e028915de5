"""
Client SDK for Call Queue Orchestrator
Provides easy-to-use interface for publishing calls and monitoring
"""

import json
import asyncio
from typing import List, Dict, Optional
from datetime import datetime

import aiohttp
from google.cloud import pubsub_v1
from pydantic import BaseModel, Field


class CallRequest(BaseModel):
    """Model for a call request"""
    from_number: str = Field(..., pattern=r"^\+\d{10,15}$")
    to_number: str = Field(..., pattern=r"^\+\d{10,15}$")
    mission: str
    organisation_id: str
    campaign_id: str
    booking_id: int
    customer_name: str
    priority: int = Field(default=0, ge=0, le=10)  # 0=lowest, 10=highest
    metadata: Dict = Field(default_factory=dict)


class CallQueueClient:
    """Client for interacting with Call Queue Orchestrator"""
    
    def __init__(
        self,
        project_id: str,
        topic_name: str = "call-queue",
        api_base_url: str = "http://localhost:8000"
    ):
        self.project_id = project_id
        self.topic_name = topic_name
        self.api_base_url = api_base_url
        self.publisher = pubsub_v1.PublisherClient()
        self.topic_path = self.publisher.topic_path(project_id, topic_name)
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def queue_call(self, call: CallRequest) -> str:
        """Queue a single call"""
        message_data = json.dumps(call.dict()).encode("utf-8")
        
        # Add attributes for filtering/routing
        attributes = {
            "priority": str(call.priority),
            "organisation_id": call.organisation_id,
            "campaign_id": call.campaign_id
        }
        
        future = self.publisher.publish(
            self.topic_path,
            message_data,
            **attributes
        )
        
        message_id = future.result()
        return message_id
    
    def queue_calls_batch(self, calls: List[CallRequest]) -> List[str]:
        """Queue multiple calls in batch"""
        message_ids = []
        
        # Use batch publishing for efficiency
        batch = self.publisher.batch()
        futures = []
        
        for call in calls:
            message_data = json.dumps(call.dict()).encode("utf-8")
            attributes = {
                "priority": str(call.priority),
                "organisation_id": call.organisation_id,
                "campaign_id": call.campaign_id
            }
            
            future = batch.publish(message_data, **attributes)
            futures.append(future)
        
        # Commit the batch
        batch.commit()
        
        # Get message IDs
        for future in futures:
            message_ids.append(future.result())
        
        return message_ids
    
    async def get_status(self) -> Dict:
        """Get orchestrator status"""
        if not self.session:
            raise RuntimeError("Client not initialized. Use 'async with' context manager.")
        
        async with self.session.get(f"{self.api_base_url}/status") as resp:
            if resp.status == 200:
                return await resp.json()
            else:
                raise Exception(f"Failed to get status: {resp.status}")
    
    async def get_call_details(self, call_id: str) -> Dict:
        """Get details of a specific call"""
        if not self.session:
            raise RuntimeError("Client not initialized. Use 'async with' context manager.")
        
        async with self.session.get(f"{self.api_base_url}/calls/{call_id}") as resp:
            if resp.status == 200:
                return await resp.json()
            elif resp.status == 404:
                raise ValueError(f"Call {call_id} not found")
            else:
                raise Exception(f"Failed to get call details: {resp.status}")
    
    async def retry_call(self, call_id: str) -> Dict:
        """Retry a failed call"""
        if not self.session:
            raise RuntimeError("Client not initialized. Use 'async with' context manager.")
        
        async with self.session.post(f"{self.api_base_url}/manual/retry/{call_id}") as resp:
            if resp.status == 200:
                return await resp.json()
            elif resp.status == 404:
                raise ValueError(f"Call {call_id} not found")
            elif resp.status == 503:
                raise Exception("System at capacity, cannot retry now")
            else:
                raise Exception(f"Failed to retry call: {resp.status}")
    
    async def wait_for_capacity(self, required_slots: int = 1, timeout: int = 60):
        """Wait until system has required capacity"""
        start_time = asyncio.get_event_loop().time()
        
        while asyncio.get_event_loop().time() - start_time < timeout:
            status = await self.get_status()
            
            if status["available_slots"] >= required_slots:
                return True
            
            await asyncio.sleep(1)
        
        return False
    
    async def monitor_campaign(
        self,
        campaign_id: str,
        callback=None,
        poll_interval: int = 5
    ):
        """Monitor a campaign's progress"""
        if not self.session:
            raise RuntimeError("Client not initialized. Use 'async with' context manager.")
        
        while True:
            # In production, you'd query Redis for campaign-specific stats
            status = await self.get_status()
            
            if callback:
                should_continue = await callback(status)
                if not should_continue:
                    break
            
            await asyncio.sleep(poll_interval)


# Usage Examples

async def example_basic_usage():
    """Basic usage example"""
    
    # Initialize client
    async with CallQueueClient(
        project_id="your-project-id",
        api_base_url="http://localhost:8000"
    ) as client:
        
        # Create a call request
        call = CallRequest(
            from_number="+***********",
            to_number="+************",
            mission="Lead generation",
            organisation_id="jazzcash",
            campaign_id="EFU Sales",
            booking_id=34,
            customer_name="Faraz Shahid",
            priority=5
        )
        
        # Queue the call
        message_id = client.queue_call(call)
        print(f"Queued call with message ID: {message_id}")
        
        # Check system status
        status = await client.get_status()
        print(f"System status: {status}")


async def example_batch_campaign():
    """Example of running a batch campaign"""
    
    async with CallQueueClient(
        project_id="your-project-id"
    ) as client:
        
        # Load call list (from CSV, database, etc.)
        call_list = [
            CallRequest(
                from_number="+***********",
                to_number=f"+92303444{5000 + i:04d}",
                mission="Sales campaign",
                organisation_id="company_x",
                campaign_id="Q4_2024_Sales",
                booking_id=1000 + i,
                customer_name=f"Customer {i}",
                priority=3
            )
            for i in range(100)  # 100 calls
        ]
        
        # Queue calls in batches
        batch_size = 20
        for i in range(0, len(call_list), batch_size):
            batch = call_list[i:i + batch_size]
            
            # Wait for capacity if needed
            await client.wait_for_capacity(required_slots=5)
            
            # Queue the batch
            message_ids = client.queue_calls_batch(batch)
            print(f"Queued batch {i//batch_size + 1}: {len(message_ids)} calls")
            
            # Small delay between batches
            await asyncio.sleep(2)
        
        print("Campaign queued successfully!")


async def example_priority_routing():
    """Example with priority-based routing"""
    
    async with CallQueueClient(
        project_id="your-project-id"
    ) as client:
        
        # High priority VIP call
        vip_call = CallRequest(
            from_number="+***********",
            to_number="+************",
            mission="VIP customer support",
            organisation_id="premium_org",
            campaign_id="VIP_Support",
            booking_id=9999,
            customer_name="VIP Customer",
            priority=10,  # Highest priority
            metadata={"vip": True, "account_value": "high"}
        )
        
        # Regular call
        regular_call = CallRequest(
            from_number="+***********",
            to_number="+************",
            mission="General inquiry",
            organisation_id="standard_org",
            campaign_id="General_Support",
            booking_id=1000,
            customer_name="Regular Customer",
            priority=3
        )
        
        # Queue both calls
        vip_id = client.queue_call(vip_call)
        regular_id = client.queue_call(regular_call)
        
        print(f"VIP call queued: {vip_id}")
        print(f"Regular call queued: {regular_id}")


async def example_monitoring():
    """Example of monitoring campaign progress"""
    
    calls_completed = 0
    
    async def progress_callback(status):
        nonlocal calls_completed
        
        active = status["active_calls"]
        processed = status.get("total_processed", 0)
        
        if processed > calls_completed:
            newly_completed = processed - calls_completed
            calls_completed = processed
            print(f"Progress: {newly_completed} calls completed")
            print(f"Active: {active}, Total: {processed}")
        
        # Continue monitoring if there are active calls
        return active > 0 or status["available_slots"] < 30
    
    async with CallQueueClient(
        project_id="your-project-id"
    ) as client:
        
        # Start monitoring
        await client.monitor_campaign(
            campaign_id="Q4_2024_Sales",
            callback=progress_callback,
            poll_interval=5
        )
        
        print(f"Campaign completed! Total calls: {calls_completed}")


async def example_error_handling():
    """Example with comprehensive error handling"""
    
    async with CallQueueClient(
        project_id="your-project-id"
    ) as client:
        
        calls_to_retry = []
        
        # Process calls with error handling
        for i in range(10):
            call = CallRequest(
                from_number="+***********",
                to_number=f"+92303444{6000 + i:04d}",
                mission="Sales",
                organisation_id="test_org",
                campaign_id="test_campaign",
                booking_id=2000 + i,
                customer_name=f"Customer {i}"
            )
            
            try:
                # Try to queue the call
                message_id = client.queue_call(call)
                print(f"✓ Call {i} queued: {message_id}")
                
            except Exception as e:
                print(f"✗ Failed to queue call {i}: {e}")
                calls_to_retry.append(call)
        
        # Retry failed calls after a delay
        if calls_to_retry:
            print(f"\nRetrying {len(calls_to_retry)} failed calls...")
            await asyncio.sleep(5)
            
            for call in calls_to_retry:
                try:
                    message_id = client.queue_call(call)
                    print(f"✓ Retry successful: {message_id}")
                except Exception as e:
                    print(f"✗ Retry failed: {e}")


# CSV Import Example
import csv

async def import_from_csv(csv_file: str, client: CallQueueClient):
    """Import calls from CSV file"""
    
    calls = []
    
    with open(csv_file, 'r') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            call = CallRequest(
                from_number=row['from_number'],
                to_number=row['to_number'],
                mission=row['mission'],
                organisation_id=row['organisation_id'],
                campaign_id=row['campaign_id'],
                booking_id=int(row['booking_id']),
                customer_name=row['customer_name'],
                priority=int(row.get('priority', 0))
            )
            calls.append(call)
    
    print(f"Loaded {len(calls)} calls from CSV")
    
    # Queue in batches
    batch_size = 50
    for i in range(0, len(calls), batch_size):
        batch = calls[i:i + batch_size]
        message_ids = client.queue_calls_batch(batch)
        print(f"Queued batch: {len(message_ids)} calls")
        await asyncio.sleep(1)
    
    print("CSV import complete!")


if __name__ == "__main__":
    # Run examples
    asyncio.run(example_basic_usage())
    # asyncio.run(example_batch_campaign())
    # asyncio.run(example_priority_routing())
    # asyncio.run(example_monitoring())
    # asyncio.run(example_error_handling())