"""
Script to load your 10 actual phone numbers into Pub/Sub for testing
"""

import json
from google.cloud import pubsub_v1

# Configuration
PROJECT_ID = "noble-courier-418207"  # Replace with your GCP project
TOPIC_NAME = "call-orchestrator"        # Your Pub/Sub topic name
# FROM_NUMBER = "+12314987814"     # Your Twilio phone number
FROM_NUMBER = "+924233330335"
# FROM_NUMBER = "+923349589089"

def quick_load():
    # Read numbers
    with open("numbers.txt", "r") as f:
        numbers = [line.strip() for line in f if line.strip() and not line.startswith("#")][:10]
    
    # Read names (optional)
    try:
        with open("names.txt", "r") as f:
            names = [line.strip() for line in f if line.strip() and not line.startswith("#")][:10]
    except:
        names = [f"Customer {i+1}" for i in range(10)]
    
    # Ensure we have 10 of each
    while len(names) < len(numbers):
        names.append(f"Customer {len(names) + 1}")
    
    # Create calls
    publisher = pubsub_v1.PublisherClient()
    topic_path = publisher.topic_path(PROJECT_ID, TOPIC_NAME)
    
    print(f"Publishing {len(numbers)} calls to Pub/Sub...")
    
    for i, (number, name) in enumerate(zip(numbers, names)):
        call = {
            "from_number": FROM_NUMBER,
            "to_number": number,
            "mission": "Lead generation",
            "organisation_id": "jazzcash",
            "campaign_id": "EFU Sales",
            "booking_id": 100 + i,
            "customer_name": name
        }
        
        message_data = json.dumps(call).encode("utf-8")
        future = publisher.publish(topic_path, message_data)
        message_id = future.result()
        
        print(f"✅ {i+1}. {name:20} → {number}")
    
    print(f"\n🎉 Done! Published {len(numbers)} calls.")

if __name__ == "__main__":
    quick_load()