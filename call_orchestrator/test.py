"""
Call Queue Orchestrator Service
Manages concurrent call processing with Pub/Sub, rate limiting, and PostgreSQL persistence
"""

import asyncio
import json
import logging
import uuid
import os
from datetime import datetime, timedelta
from typing import Dict, Optional, Set
from contextlib import asynccontextmanager
from enum import Enum

import aiohttp
import redis.asyncio as redis
import asyncpg
from fastapi import FastAPI, HTTPException, BackgroundTasks, Response
from pydantic import BaseModel
from google.cloud import pubsub_v1
from google.api_core import retry

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
class Config:
    # Pub/Sub settings
    PROJECT_ID = "noble-courier-418207"
    SUBSCRIPTION_NAME = "call-orchestrator-sub"
    
    # Redis settings
    REDIS_URL = "redis://localhost:6379"
    
    # Call service settings
    OUTBOUND_CALLER_SERVICE_URL = "https://outbound-call-************.us-central1.run.app/make_call/"
    WEBHOOK_BASE_URL = "https://de5300158eb5.ngrok-free.app"
    
    # Concurrency settings
    MAX_CONCURRENT_CALLS = 5
    CALL_TIMEOUT_SECONDS = 600  # 10 minutes max call duration
    
    # Retry settings
    MAX_RETRIES = 3
    RETRY_DELAY_SECONDS = 5


class CallStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    BUSY = "busy"
    NO_ANSWER = "no-answer"


class CallRecord(BaseModel):
    """Model for call record from Pub/Sub"""
    from_number: str
    to_number: str
    mission: str
    organisation_id: str
    campaign_id: str
    booking_id: int
    customer_name: str


class StatusCallback(BaseModel):
    """Model for status callback from outbound service"""
    call_id: str
    status: str
    duration: Optional[int] = None
    recording_url: Optional[str] = None
    error_message: Optional[str] = None
    timestamp: Optional[str] = None


async def dump_call_to_postgres(db_pool, call_id: str, call_data: dict, callback_data: dict):
    """
    Simple function to dump call data to PostgreSQL call_logs table
    """
    try:
        if not db_pool:
            logger.warning("Database pool not available, skipping database dump")
            return
            
        async with db_pool.acquire() as connection:
            # Parse timestamps
            created_at = datetime.fromisoformat(call_data.get('created_at', datetime.utcnow().isoformat()))
            initiated_at = datetime.fromisoformat(call_data.get('initiated_at')) if call_data.get('initiated_at') else None
            completed_at = datetime.fromisoformat(callback_data['timestamp']) if callback_data.get('timestamp') else None
            
            # Check if we have full call data or just callback data
            if all(key in call_data for key in ['from_number', 'to_number', 'mission', 'organisation_id', 'campaign_id', 'booking_id', 'customer_name']):
                # We have full call data - insert complete record
                insert_sql = """
                INSERT INTO chatbot.call_logs (
                    call_id, from_number, to_number, mission, organisation_id,
                    campaign_id, booking_id, customer_name, status, duration,
                    recording_url, error_message, created_at, initiated_at, completed_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
                ON CONFLICT (call_id) DO UPDATE SET
                    status = EXCLUDED.status,
                    duration = EXCLUDED.duration,
                    recording_url = EXCLUDED.recording_url,
                    error_message = EXCLUDED.error_message,
                    completed_at = EXCLUDED.completed_at
                """
                
                await connection.execute(
                    insert_sql,
                    call_id,
                    call_data['from_number'],
                    call_data['to_number'],
                    call_data['mission'],
                    call_data['organisation_id'],
                    call_data['campaign_id'],
                    int(call_data['booking_id']),
                    call_data['customer_name'],
                    callback_data['status'],
                    callback_data.get('duration'),
                    callback_data.get('recording_url'),
                    callback_data.get('error_message'),
                    created_at,
                    initiated_at,
                    completed_at
                )
            else:
                # Only have callback data - update existing record or insert minimal record
                logger.warning(f"Only callback data available for call {call_id}, updating status only")
                update_sql = """
                INSERT INTO call_logs (call_id, status, duration, recording_url, error_message, completed_at)
                VALUES ($1, $2, $3, $4, $5, $6)
                ON CONFLICT (call_id) DO UPDATE SET
                    status = EXCLUDED.status,
                    duration = EXCLUDED.duration,
                    recording_url = EXCLUDED.recording_url,
                    error_message = EXCLUDED.error_message,
                    completed_at = EXCLUDED.completed_at
                """
                
                await connection.execute(
                    update_sql,
                    call_id,
                    callback_data['status'],
                    callback_data.get('duration'),
                    callback_data.get('recording_url'),
                    callback_data.get('error_message'),
                    completed_at
                )
            
            logger.info(f"Call record {call_id} dumped to PostgreSQL successfully")
            
    except Exception as e:
        logger.error(f"Error dumping call record to PostgreSQL: {e}")
        # Don't raise - we don't want database issues to break the orchestrator


class CallOrchestrator:
    """Main orchestrator managing call concurrency and processing"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.subscriber: Optional[pubsub_v1.SubscriberClient] = None
        self.subscription_path: Optional[str] = None
        self.active_calls: Set[str] = set()
        self.processing = False
        self.session: Optional[aiohttp.ClientSession] = None
        self._processing_lock = asyncio.Lock()
        self._pull_task: Optional[asyncio.Task] = None
        
    async def initialize(self):
        """Initialize connections and resources"""
        try:
            # Initialize Redis
            self.redis_client = redis.from_url(
                Config.REDIS_URL,
                encoding="utf-8",
                decode_responses=True
            )
            await self.redis_client.ping()
            logger.info("Redis connection established")
            
            # Initialize Pub/Sub
            self.subscriber = pubsub_v1.SubscriberClient()
            self.subscription_path = self.subscriber.subscription_path(
                Config.PROJECT_ID,
                Config.SUBSCRIPTION_NAME
            )
            logger.info(f"Pub/Sub subscription initialized: {self.subscription_path}")
            
            # Initialize HTTP session
            self.session = aiohttp.ClientSession()
            
            # Load active calls from Redis (for recovery)
            await self._recover_active_calls()
            
            self.processing = True
            logger.info("Call orchestrator initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize orchestrator: {e}")
            raise
    
    async def _recover_active_calls(self):
        """Recover active calls from Redis on startup"""
        try:
            active_call_keys = await self.redis_client.keys("call:*:data")
            for key in active_call_keys:
                call_id = key.split(":")[1]
                self.active_calls.add(call_id)
            logger.info(f"Recovered {len(self.active_calls)} active calls")
        except Exception as e:
            logger.error(f"Error recovering active calls: {e}")
    
    async def start_processing(self):
        """Start the main processing loop"""
        self._pull_task = asyncio.create_task(self._process_queue())
        # Start timeout checker
        asyncio.create_task(self._check_timeouts())
        logger.info("Started call processing")
    
    async def stop_processing(self):
        """Gracefully stop processing"""
        self.processing = False
        if self._pull_task:
            await self._pull_task
        
        # Wait for active calls to complete
        timeout = 30
        start_time = asyncio.get_event_loop().time()
        while self.active_calls and (asyncio.get_event_loop().time() - start_time) < timeout:
            logger.info(f"Waiting for {len(self.active_calls)} active calls to complete...")
            await asyncio.sleep(1)
        
        # Close connections
        if self.session:
            await self.session.close()
        if self.redis_client:
            await self.redis_client.close()
        
        logger.info("Orchestrator stopped")
    
    async def _process_queue(self):
        """Main queue processing loop"""
        while self.processing:
            try:
                # Check if we have capacity
                current_count = len(self.active_calls)
                
                if current_count < Config.MAX_CONCURRENT_CALLS:
                    # Calculate how many messages to pull
                    messages_to_pull = min(
                        Config.MAX_CONCURRENT_CALLS - current_count,
                        10  # Max messages per pull request
                    )
                    
                    # Pull messages from Pub/Sub
                    messages = await self._pull_messages(messages_to_pull)
                    
                    if messages:
                        # Process messages concurrently
                        tasks = [self._process_message(msg) for msg in messages]
                        await asyncio.gather(*tasks, return_exceptions=True)
                    else:
                        # No messages available, wait before trying again
                        await asyncio.sleep(1)
                else:
                    # At capacity, wait a bit before checking again
                    await asyncio.sleep(0.5)
                    
            except Exception as e:
                logger.error(f"Error in processing loop: {e}")
                await asyncio.sleep(5)
    
    async def _pull_messages(self, max_messages: int):
        """Pull messages from Pub/Sub"""
        try:
            # Run synchronous Pub/Sub pull in executor
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.subscriber.pull(
                    request={
                        "subscription": self.subscription_path,
                        "max_messages": max_messages,
                        "return_immediately": True,
                    },
                    retry=retry.Retry(deadline=5),
                )
            )
            
            return response.received_messages if response.received_messages else []
            
        except Exception as e:
            logger.error(f"Error pulling messages: {e}")
            return []
    
    async def _process_message(self, message):
        """Process a single message from Pub/Sub"""
        try:
            # Parse message data
            call_data = json.loads(message.message.data.decode('utf-8'))
            call_record = CallRecord(**call_data)
            
            # Generate unique call ID
            call_id = str(uuid.uuid4())
            
            # Add to active calls
            async with self._processing_lock:
                if len(self.active_calls) >= Config.MAX_CONCURRENT_CALLS:
                    # Nack the message to retry later
                    await self._nack_message(message)
                    return
                
                self.active_calls.add(call_id)
            
            # Store call data in Redis
            await self._store_call_data(call_id, call_record)
            
            # Initiate the call
            success = await self._initiate_call(call_id, call_record)
            
            if success:
                # Acknowledge the message
                await self._ack_message(message)
                logger.info(f"Call {call_id} initiated successfully for {call_record.to_number}")
            else:
                # Failed to initiate, clean up and nack
                await self._cleanup_call(call_id)
                await self._nack_message(message)
                
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            await self._nack_message(message)
    
    async def _store_call_data(self, call_id: str, call_record: CallRecord):
        """Store call data in Redis"""
        try:
            call_data = {
                **call_record.dict(),
                "call_id": call_id,
                "status": CallStatus.PENDING,
                "created_at": datetime.utcnow().isoformat(),
                "attempts": 0
            }
            
            # Store with expiration (2x timeout duration)
            await self.redis_client.hset(
                f"call:{call_id}:data",
                mapping=call_data
            )
            await self.redis_client.expire(
                f"call:{call_id}:data",
                Config.CALL_TIMEOUT_SECONDS * 2
            )
            
            # Add to active calls set
            await self.redis_client.sadd("active_calls", call_id)
            
        except Exception as e:
            logger.error(f"Error storing call data: {e}")
            raise
    
    async def _initiate_call(self, call_id: str, call_record: CallRecord) -> bool:
        """Send POST request to outbound caller service"""
        try:
            webhook_url = f"{Config.WEBHOOK_BASE_URL}/webhook/status/{call_id}"
            
            payload = {
                **call_record.dict(),
                "call_id": call_id,
                "webhook_url": webhook_url
            }
            
            async with self.session.post(
                Config.OUTBOUND_CALLER_SERVICE_URL,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status == 200:
                    # Update status in Redis
                    await self.redis_client.hset(
                        f"call:{call_id}:data",
                        mapping={
                            "status": CallStatus.IN_PROGRESS,
                            "initiated_at": datetime.utcnow().isoformat()
                        }
                    )
                    return True
                else:
                    logger.error(f"Failed to initiate call: {response.status}")
                    return False
                    
        except asyncio.TimeoutError:
            logger.error(f"Timeout initiating call {call_id}")
            return False
        except Exception as e:
            logger.error(f"Error initiating call {call_id}: {e}")
            return False
    
    async def handle_status_callback(self, call_id: str, callback: StatusCallback, db_pool):
        """Handle status callback from outbound service"""
        try:
            # Check if call exists
            if call_id not in self.active_calls:
                logger.warning(f"Received callback for unknown call: {call_id}")
                return
            
            # Update call status in Redis
            update_data = {
                "status": callback.status,
                "completed_at": datetime.utcnow().isoformat()
            }
            
            if callback.duration:
                update_data["duration"] = callback.duration
            if callback.recording_url:
                update_data["recording_url"] = callback.recording_url
            if callback.error_message:
                update_data["error_message"] = callback.error_message
            
            await self.redis_client.hset(
                f"call:{call_id}:data",
                mapping=update_data
            )
            
            # Store callback history in Redis
            await self._store_callback_history(call_id, callback)
            
            # If call is complete, dump to database and cleanup
            if callback.status in ["completed", "failed", "busy", "no-answer", "timeout"]:
                # Get complete call data for database dump
                call_data = await self.redis_client.hgetall(f"call:{call_id}:data")
                
                # Dump to PostgreSQL
                await dump_call_to_postgres(db_pool, call_id, call_data, callback.dict())
                
                # Cleanup from active tracking
                await self._cleanup_call(call_id)
                logger.info(f"Call {call_id} completed with status: {callback.status} and dumped to database")
            
        except Exception as e:
            logger.error(f"Error handling status callback: {e}")
    
    async def _store_callback_history(self, call_id: str, callback: StatusCallback):
        """Store callback history for auditing"""
        try:
            callback_key = f"call:{call_id}:callbacks"
            await self.redis_client.rpush(
                callback_key,
                json.dumps(callback.dict())
            )
            await self.redis_client.expire(callback_key, 86400 * 7)  # Keep for 7 days
        except Exception as e:
            logger.error(f"Error storing callback history: {e}")
    
    async def _cleanup_call(self, call_id: str):
        """Remove call from active tracking"""
        async with self._processing_lock:
            self.active_calls.discard(call_id)
        
        # Remove from Redis active set
        await self.redis_client.srem("active_calls", call_id)
        
        logger.info(f"Cleaned up call {call_id}. Active calls: {len(self.active_calls)}")
    
    async def _check_timeouts(self, db_pool=None):
        """Periodically check for timed out calls"""
        while self.processing:
            try:
                current_time = datetime.utcnow()
                
                for call_id in list(self.active_calls):
                    call_data = await self.redis_client.hgetall(f"call:{call_id}:data")
                    
                    if call_data and "initiated_at" in call_data:
                        initiated_at = datetime.fromisoformat(call_data["initiated_at"])
                        
                        if (current_time - initiated_at).total_seconds() > Config.CALL_TIMEOUT_SECONDS:
                            logger.warning(f"Call {call_id} timed out")
                            
                            # Update status
                            timeout_data = {
                                "status": CallStatus.TIMEOUT,
                                "timeout_at": current_time.isoformat(),
                                "completed_at": current_time.isoformat()
                            }
                            
                            await self.redis_client.hset(
                                f"call:{call_id}:data",
                                mapping=timeout_data
                            )
                            
                            # Get updated call data and dump to database
                            updated_call_data = await self.redis_client.hgetall(f"call:{call_id}:data")
                            timeout_callback = {
                                "status": "timeout",
                                "timestamp": current_time.isoformat()
                            }
                            await dump_call_to_postgres(db_pool, call_id, updated_call_data, timeout_callback)
                            
                            # Cleanup
                            await self._cleanup_call(call_id)
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error checking timeouts: {e}")
                await asyncio.sleep(30)
    
    async def _ack_message(self, message):
        """Acknowledge a Pub/Sub message"""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.subscriber.acknowledge(
                    request={
                        "subscription": self.subscription_path,
                        "ack_ids": [message.ack_id],
                    }
                )
            )
        except Exception as e:
            logger.error(f"Error acknowledging message: {e}")
    
    async def _nack_message(self, message):
        """Negative acknowledge a Pub/Sub message"""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.subscriber.modify_ack_deadline(
                    request={
                        "subscription": self.subscription_path,
                        "ack_ids": [message.ack_id],
                        "ack_deadline_seconds": 0,
                    }
                )
            )
        except Exception as e:
            logger.error(f"Error nacking message: {e}")
    
    async def get_status(self) -> dict:
        """Get current orchestrator status"""
        try:
            active_count = len(self.active_calls)
            
            # Get additional stats from Redis
            total_processed = await self.redis_client.get("stats:total_processed") or 0
            total_failed = await self.redis_client.get("stats:total_failed") or 0
            
            return {
                "active_calls": active_count,
                "max_concurrent": Config.MAX_CONCURRENT_CALLS,
                "available_slots": Config.MAX_CONCURRENT_CALLS - active_count,
                "total_processed": int(total_processed),
                "total_failed": int(total_failed),
                "processing": self.processing
            }
        except Exception as e:
            logger.error(f"Error getting status: {e}")
            return {"error": str(e)}


# Initialize orchestrator
orchestrator = CallOrchestrator()


@asynccontextmanager
async def lifespan(app):
    """
    FastAPI lifespan context manager for database connection pool management.
    Handles startup and shutdown of the database connection pool.
    """
    logger.info("Application startup: Initializing database connection pool...")
    try:
        # Create pool using env vars with SSL for AWS RDS
        app.state.db_pool = await asyncpg.create_pool(
            user=os.getenv('user', 'postgres'),
            password=os.getenv('password', 'najoomi123'),
            database=os.getenv('dbname', 'call_centre_analytics'),
            host=os.getenv('host', 'najoomi.clw2kwwaavrk.us-east-1.rds.amazonaws.com'),
            port=os.getenv('port', '5432'),
            min_size=5,
            max_size=15,  # Good for 30+ concurrent calls
            ssl='require',  # Required for AWS RDS
            command_timeout=60
        )
        logger.info("Database connection pool created successfully.")
    except Exception as e:
        logger.critical(f"CRITICAL: Failed to create database connection pool during startup: {e}", exc_info=True)
        # App can start, but DB features will fail until DB is up.
        app.state.db_pool = None  # Ensure state is None if creation failed

    # Initialize orchestrator
    await orchestrator.initialize()
    await orchestrator.start_processing()
    
    yield  # Application runs here

    # === Shutdown ===
    logger.info("Application shutdown: Closing database connection pool...")
    
    # Stop orchestrator
    await orchestrator.stop_processing()
    
    # Close database pool
    pool_to_close = getattr(app.state, "db_pool", None)
    if pool_to_close:
        try:
            await pool_to_close.close()
            logger.info("Database connection pool closed.")
        except Exception as e:
            logger.error(f"Failed to close database connection pool properly: {e}")


app = FastAPI(title="Call Queue Orchestrator", lifespan=lifespan)


from fastapi import Form

@app.post("/webhook/status/{call_id}")
async def status_webhook(
    call_id: str,
    CallSid: str = Form(...),
    CallStatus: str = Form(...),
    CallDuration: str = Form(None),
    From: str = Form(None),
    To: str = Form(None),
    background_tasks: BackgroundTasks = None
):
    """Handle status callbacks from Twilio"""
    
    # Map Twilio status to your internal status
    status_mapping = {
        "completed": "completed",
        "busy": "busy",
        "no-answer": "no-answer",
        "failed": "failed",
        "canceled": "failed"
    }
    
    callback = StatusCallback(
        call_id=call_id,
        status=status_mapping.get(CallStatus, CallStatus),
        duration=int(CallDuration) if CallDuration else None,
        timestamp=datetime.utcnow().isoformat()
    )
    
    # Process callback asynchronously
    background_tasks.add_task(
        orchestrator.handle_status_callback,
        call_id,
        callback,
        app.state.db_pool
    )
    
    # Twilio expects a 200 OK response
    return Response(status_code=200)


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    status = await orchestrator.get_status()
    db_status = "connected" if app.state.db_pool else "disconnected"
    return {
        "status": "healthy" if orchestrator.processing else "unhealthy",
        "database": db_status,
        "details": status
    }


@app.get("/status")
async def get_status():
    """Get current orchestrator status"""
    return await orchestrator.get_status()


@app.get("/calls/{call_id}")
async def get_call_details(call_id: str):
    """Get details of a specific call"""
    try:
        call_data = await orchestrator.redis_client.hgetall(f"call:{call_id}:data")
        if not call_data:
            raise HTTPException(status_code=404, detail="Call not found")
        
        callbacks = await orchestrator.redis_client.lrange(f"call:{call_id}:callbacks", 0, -1)
        call_data["callbacks"] = [json.loads(cb) for cb in callbacks]
        
        return call_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting call details: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/manual/retry/{call_id}")
async def manual_retry(call_id: str):
    """Manually retry a failed call"""
    try:
        # Get call data
        call_data = await orchestrator.redis_client.hgetall(f"call:{call_id}:data")
        if not call_data:
            raise HTTPException(status_code=404, detail="Call not found")
        
        # Create new call with same data
        new_call_id = str(uuid.uuid4())
        call_record = CallRecord(
            from_number=call_data["from_number"],
            to_number=call_data["to_number"],
            mission=call_data["mission"],
            organisation_id=call_data["organisation_id"],
            campaign_id=call_data["campaign_id"],
            booking_id=int(call_data["booking_id"]),
            customer_name=call_data["customer_name"]
        )
        
        # Check capacity
        if len(orchestrator.active_calls) >= Config.MAX_CONCURRENT_CALLS:
            raise HTTPException(status_code=503, detail="System at capacity")
        
        # Process the retry
        orchestrator.active_calls.add(new_call_id)
        await orchestrator._store_call_data(new_call_id, call_record)
        success = await orchestrator._initiate_call(new_call_id, call_record)
        
        if success:
            return {"status": "retried", "new_call_id": new_call_id}
        else:
            await orchestrator._cleanup_call(new_call_id)
            raise HTTPException(status_code=500, detail="Failed to initiate retry")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrying call: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)