from twilio.rest import Client

# Your Account SID and Auth Token
account_sid = "**********************************"
auth_token = "e380e5cc9cc2bb3ff9e241b477a18ea7"
client = Client(account_sid, auth_token)

# The local phone number you want to whitelist
phone_number_to_verify = "+************"  # Replace with your local number
friendly_name = "EFU/Najoomi Outbound Number"

try:
    # This API call will trigger a phone call to your number
    validation_request = client.validation_requests.create(
        friendly_name=friendly_name,
        phone_number=phone_number_to_verify
    )
    
    print(f"Verification call initiated to {phone_number_to_verify}.")
    print("Please answer the call and enter the 6-digit code on your keypad.")
    print(f"The verification code is: {validation_request.validation_code}")

except Exception as e:
    print(f"An error occurred: {e}")