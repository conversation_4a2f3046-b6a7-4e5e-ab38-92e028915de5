from fastapi import FastAPI, Form, Response
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()

@app.post("/log_digits")
async def log_digits(Digits: str = Form(None)):
    """
    This endpoint receives the POST request from <PERSON><PERSON><PERSON>'s <Gather> verb.
    It logs the digits that were pressed by the caller.
    """
    if Digits:
        logger.info(f"✅ SUCCESS: Received DTMF digits: {Digits}")
        
        # Respond with TwiML to thank the user and hang up
        twiml_response = f"""
        <Response>
            <Say>Thank you. You entered {', '.join(list(Digits))}. Goodbye.</Say>
            <Hangup/>
        </Response>
        """
        return Response(content=twiml_response, media_type="application/xml")
    else:
        logger.warning("⚠️ WARNING: <PERSON><PERSON><PERSON> called the webhook but no digits were received.")
        
        twiml_response = """
        <Response>
            <Say>No digits were detected. Goodbye.</Say>
            <Hangup/>
        </Response>
        """
        return Response(content=twiml_response, media_type="application/xml")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)