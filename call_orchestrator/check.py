import os
from twilio.rest import Client
from twilio.base.exceptions import TwilioRestException

def is_number_verified(phone_number_to_check: str) -> bool:
    """
    Checks if a given phone number is a verified caller ID in your Twilio account.

    Args:
        phone_number_to_check: The phone number to check, in E.164 format (e.g., "+***********").

    Returns:
        True if the number is verified, False otherwise.
    """
    try:
        # --- IMPORTANT ---
        # Replace these with your actual Twilio Account SID and Auth Token.
        # It's best to load these from environment variables for security.
        account_sid = "**********************************"
        auth_token = "e380e5cc9cc2bb3ff9e241b477a18ea7"
        
        if account_sid == "YOUR_ACCOUNT_SID" or auth_token == "YOUR_AUTH_TOKEN":
            print("ERROR: Please set your TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN.")
            return False

        client = Client(account_sid, auth_token)

        # Fetch all verified caller IDs from your account
        outgoing_caller_ids = client.outgoing_caller_ids.list()

        # print(f'ALL VERIFIED NUMBERS: {outgoing_caller_ids}')

        # Check if the number exists in the list
        for caller_id in outgoing_caller_ids:
            if caller_id.phone_number == phone_number_to_check:
                print(f"✅ Success: Found matching verified number: {caller_id.phone_number}")
                return True
        
        print(f"❌ Not Found: The number {phone_number_to_check} is not a verified caller ID.")
        return False

    except TwilioRestException as e:
        print(f"Twilio API Error: {e}")
        return False
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return False

# --- How to use the function ---
if __name__ == "__main__":
    # Replace this with the number you want to check
    number_to_check = "+************"  
    
    verified = is_number_verified(number_to_check)
    
    if verified:
        print(f"The number {number_to_check} is ready to be used as a Caller ID.")
    else:
        print(f"The number {number_to_check} needs to be verified before it can be used.")
# import os
# from twilio.rest import Client
# from twilio.base.exceptions import TwilioRestException

# def list_verified_numbers():
#     """
#     Fetches and prints all verified caller IDs from a Twilio account.
#     """
#     try:
#         # --- IMPORTANT ---
#         # It's best to load your credentials from environment variables for security.
#         account_sid = "**********************************"
#         auth_token = "e380e5cc9cc2bb3ff9e241b477a18ea7"
        
#         if not account_sid or not auth_token:
#             print("ERROR: Please set your TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN environment variables.")
#             return

#         client = Client(account_sid, auth_token)

#         print("Fetching verified caller IDs...")
#         verified_numbers = client.outgoing_caller_ids.list()

#         if not verified_numbers:
#             print("No verified numbers found in this account.")
#             return

#         print("\n--- Verified Numbers ---")
#         for number in verified_numbers:
#             print(f"Number: {number.phone_number}, Name: {number.friendly_name}")
#         print("------------------------")

#     except TwilioRestException as e:
#         print(f"Twilio API Error: {e}")
#         print("Please check if your Account SID and Auth Token are correct.")
#     except Exception as e:
#         print(f"An unexpected error occurred: {e}")

# # --- How to use the function ---
# if __name__ == "__main__":
#     list_verified_numbers()
