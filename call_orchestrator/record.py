import requests

# Your Twilio credentials
account_sid = "**********************************"
auth_token = "e380e5cc9cc2bb3ff9e241b477a18ea7" # Replace with your actual Auth Token

# The recording URL
recording_url = "https://api.twilio.com/2010-04-01/Accounts/**********************************/Recordings/RE49193f3840b2f4f6465d5cae0b8fd2ca"

# Make an authenticated GET request
response = requests.get(recording_url, auth=(account_sid, auth_token))

# Check if the request was successful
if response.status_code == 200:
    # Save the recording to a file
    with open("call_recording.wav", "wb") as f:
        f.write(response.content)
    print("Recording downloaded successfully as call_recording.wav")
else:
    print(f"Failed to download recording. Status: {response.status_code}")
    print(f"Response: {response.text}")