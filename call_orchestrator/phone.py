from twilio.rest import Client
account_sid = "**********************************"
auth_token = "e380e5cc9cc2bb3ff9e241b477a18ea7"
client = Client(account_sid, auth_token)

# The local phone number you want to whitelist
# phone_number_to_verify = "+************"  

phone_number_sid = "+************" 

# The public URL where you hosted your TwiML file
twiml_url = "https://de5300158eb5.ngrok-free.app/dtmf_test.xml"

client.incoming_phone_numbers(phone_number_sid).update(
    voice_url=twiml_url,
    voice_method='POST'
)
