# 🎯 Call Orchestrator

> **Enterprise-grade call queue management system with intelligent concurrency control, real-time monitoring, and cloud-native architecture**

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![Redis](https://img.shields.io/badge/Redis-7.0+-red.svg)](https://redis.io)
[![Google Cloud](https://img.shields.io/badge/Google%20Cloud-Pub%2FSub-orange.svg)](https://cloud.google.com/pubsub)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)

## 🚀 Overview

Call Orchestrator is a high-performance, scalable service that manages outbound call campaigns with intelligent queue processing, real-time status tracking, and enterprise-grade reliability. Built for modern cloud environments, it seamlessly integrates with Twilio, Google Cloud Pub/Sub, and Redis for optimal performance.

### ✨ Key Features

- **🔄 Intelligent Queue Management** - Google Cloud Pub/Sub integration with automatic message acknowledgment
- **⚡ Concurrent Processing** - Configurable concurrency limits with smart capacity management
- **📊 Real-time Monitoring** - Live status tracking, metrics, and health checks
- **🔄 Automatic Recovery** - Fault-tolerant design with retry mechanisms and state recovery
- **🎯 Campaign Management** - Support for multi-organization campaigns with custom parameters
- **📞 Twilio Integration** - Seamless webhook handling for call status updates
- **🗄️ Persistent Storage** - Redis-backed state management with PostgreSQL logging
- **🐳 Cloud-Native** - Docker containerization with GCP Cloud Run deployment ready

## 🏗️ Architecture

```mermaid
graph TB
    A[Client Applications] --> B[Call Orchestrator API]
    B --> C[Google Cloud Pub/Sub]
    B --> D[Redis Cache]
    B --> E[PostgreSQL Database]
    B --> F[Outbound Call Service]
    F --> G[Twilio API]
    G --> H[Customer Phone]
    G --> I[Status Webhooks]
    I --> B
```

### Core Components

| Component | Purpose | Technology |
|-----------|---------|------------|
| **Queue Manager** | Message processing from Pub/Sub | Google Cloud Pub/Sub |
| **Concurrency Controller** | Manages active call limits | Redis + AsyncIO |
| **Status Tracker** | Real-time call status monitoring | Redis + Webhooks |
| **Recovery System** | Handles failures and retries | Redis + Background Tasks |
| **API Gateway** | RESTful endpoints for management | FastAPI |

## 🛠️ Installation

### Prerequisites

- Python 3.11+
- Redis 7.0+
- Google Cloud Project with Pub/Sub enabled
- Docker (optional)

### Quick Start

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd call_orchestrator
   python -m venv orch
   source orch/bin/activate  # On Windows: orch\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Environment Configuration**
   ```bash
   # Create .env file
   cp .env.example .env
   # Edit .env with your credentials
   ```

3. **Start Services**
   ```bash
   # Option 1: Docker Compose (Recommended)
   docker-compose up -d
   
   # Option 2: Local Development
   uvicorn call_orchestrator:app --host 0.0.0.0 --port 8000 --reload
   ```

## ⚙️ Configuration

### Environment Variables

```bash
# Google Cloud
PROJECT_ID=your-gcp-project-id
SUBSCRIPTION_NAME=call-orchestrator-sub
GOOGLE_APPLICATION_CREDENTIALS=/path/to/credentials.json

# Redis Configuration
REDIS_URL=redis://localhost:6379
# Or Redis Cloud
redis_host=your-redis-host
redis_port=17511
redis_username=default
redis_password=your-password

# Service URLs
OUTBOUND_CALLER_SERVICE_URL=https://your-outbound-service.com/make_call/
WEBHOOK_BASE_URL=https://your-orchestrator.com

# Performance Tuning
MAX_CONCURRENT_CALLS=10
CALL_TIMEOUT_SECONDS=600
MAX_RETRIES=3
RETRY_DELAY_SECONDS=5
```

### Docker Compose Setup

```yaml
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    ports: ["6379:6379"]
    
  call-orchestrator:
    build: .
    ports: ["8000:8000"]
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on: [redis]
```

## 📡 API Reference

### Core Endpoints

#### Health & Status
```http
GET /health
GET /status
```

#### Call Management
```http
POST /webhook/status/{call_id}
POST /manual/retry/{call_id}
```

### Example Usage

```python
import aiohttp
import asyncio

async def queue_call():
    call_data = {
        "from_number": "+12314987814",
        "to_number": "+923349589089",
        "mission": "Lead generation",
        "organisation_id": "jazzcash",
        "campaign_id": "EFU Sales",
        "booking_id": 35,
        "customer_name": "Arham Anjum"
    }
    
    # Publish to Pub/Sub (handled by your client application)
    # The orchestrator will automatically process the message
```

## 🔧 Advanced Usage

### Batch Campaign Loading

Use the included `load_calls.py` script for bulk operations:

```bash
# Prepare your data
echo "+923001234567" > numbers.txt
echo "John Doe" > names.txt

# Load calls into Pub/Sub
python load_calls.py
```

### Custom Client Integration

```python
from call_orchestrator_client import CallQueueClient, CallRequest

async def run_campaign():
    async with CallQueueClient(
        project_id="your-project-id",
        api_base_url="http://localhost:8000"
    ) as client:
        
        call = CallRequest(
            from_number="+12314987814",
            to_number="+923034445823",
            mission="Lead generation",
            organisation_id="jazzcash",
            campaign_id="EFU Sales",
            booking_id=34,
            customer_name="Faraz Shahid"
        )
        
        message_id = client.queue_call(call)
        status = await client.get_status()
        print(f"Queued: {message_id}, Status: {status}")
```

## 📊 Monitoring & Observability

### Real-time Metrics

- **Active Calls**: Current concurrent calls
- **Queue Depth**: Pending messages in Pub/Sub
- **Success Rate**: Call completion statistics
- **System Health**: Service availability and performance

### Status Dashboard

Access the status endpoint for real-time insights:

```json
{
  "active_calls": 7,
  "max_concurrent": 10,
  "available_slots": 3,
  "total_processed": 1247,
  "total_failed": 23,
  "processing": true
}
```

## 🚀 Deployment

### Google Cloud Run

```bash
# Build and deploy
./deploy.sh

# Or manual deployment
gcloud run deploy call-orchestrator \
  --source . \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars PROJECT_ID=your-project-id
```

### Production Considerations

- **Scaling**: Configure auto-scaling based on queue depth
- **Monitoring**: Set up Cloud Monitoring alerts
- **Security**: Use IAM roles and VPC for network isolation
- **Backup**: Regular Redis snapshots and database backups

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Wiki](../../wiki)
- **Issues**: [GitHub Issues](../../issues)
- **Discussions**: [GitHub Discussions](../../discussions)

---

<div align="center">
  <strong>Built with ❤️ for enterprise call management</strong>
</div>
