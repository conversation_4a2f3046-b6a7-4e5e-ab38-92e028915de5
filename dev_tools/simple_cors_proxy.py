#!/usr/bin/env python3
from http.server import BaseHTTPRequestHandler, HTTPServer
from urllib.parse import urlparse, parse_qs
import urllib.request
import os

PORT = int(os.getenv("PORT", "8787"))
DEFAULT_TARGET = os.getenv("TARGET_URL")  # optional fallback

CORS_HEADERS = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, X-Requested-With",
}

class ProxyHandler(BaseHTTPRequestHandler):
    def log_message(self, fmt, *args):
        # Cleaner logs
        print(f"[{self.address_string()}] {self.command} {self.path} - " + (fmt % args))

    def _write_headers(self, code=200, extra_headers=None):
        self.send_response(code)
        headers = dict(CORS_HEADERS)
        if extra_headers:
            headers.update(extra_headers)
        for k, v in headers.items():
            self.send_header(k, v)
        self.end_headers()

    def do_OPTIONS(self):
        self._write_headers(200)

    def do_GET(self):
        # Health/info page
        if self.path.startswith("/forward"):
            self._write_headers(200, {"Content-Type": "text/plain"})
            self.wfile.write(b"Send POST to /forward?url=<TARGET_URL> with JSON body.\n")
        else:
            self._write_headers(200, {"Content-Type": "text/plain"})
            self.wfile.write(f"OK. POST JSON to http://localhost:{PORT}/forward?url=https://...\n".encode())

    def do_POST(self):
        if not self.path.startswith("/forward"):
            self._write_headers(404, {"Content-Type": "text/plain"})
            self.wfile.write(b"Not Found")
            return

        # Determine target URL
        parsed = urlparse(self.path)
        qs = parse_qs(parsed.query)
        target = None
        if "url" in qs and qs["url"]:
            target = qs["url"][0]
        if not target:
            target = DEFAULT_TARGET
        if not target:
            self._write_headers(400, {"Content-Type": "text/plain"})
            self.wfile.write(b"Missing target URL. Provide ?url=... or set TARGET_URL env var.")
            return

        # Read incoming body
        length = int(self.headers.get("Content-Length", 0))
        body = self.rfile.read(length) if length else b""

        # Forward request to target
        try:
            req = urllib.request.Request(
                target,
                data=body,
                headers={"Content-Type": "application/json"},
                method="POST",
            )
            with urllib.request.urlopen(req) as resp:
                resp_body = resp.read()
                status = resp.getcode()
                ct = resp.headers.get("Content-Type", "application/json")
                self._write_headers(status, {"Content-Type": ct})
                self.wfile.write(resp_body)
        except urllib.error.HTTPError as e:
            resp_body = e.read() if hasattr(e, 'read') else str(e).encode()
            ct = e.headers.get("Content-Type", "text/plain") if hasattr(e, 'headers') and e.headers else "text/plain"
            self._write_headers(e.code, {"Content-Type": ct})
            self.wfile.write(resp_body)
        except Exception as e:
            self._write_headers(502, {"Content-Type": "text/plain"})
            self.wfile.write(f"Proxy error: {e}".encode())


def main():
    print(f"Starting simple CORS proxy on http://localhost:{PORT}")
    print("Usage: POST JSON to /forward?url=<TARGET_ENDPOINT>\n")
    with HTTPServer(("0.0.0.0", PORT), ProxyHandler) as httpd:
        httpd.serve_forever()

if __name__ == "__main__":
    main()

