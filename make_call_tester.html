<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Outbound Call API Tester</title>
  <style>
    body { font-family: system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif; max-width: 760px; margin: 24px auto; padding: 0 12px; }
    h2 { margin-top: 0; }
    label { display: block; margin-top: 10px; font-weight: 600; }
    input { width: 100%; padding: 8px 10px; font-size: 14px; box-sizing: border-box; }
    button { margin-top: 14px; padding: 10px 16px; font-size: 14px; cursor: pointer; }
    .row { display: flex; gap: 10px; }
    .row > div { flex: 1; }
    pre { background: #f6f8fa; border: 1px solid #e1e4e8; padding: 10px; white-space: pre-wrap; word-break: break-word; }
    .hint { color: #666; font-size: 12px; }
  </style>
</head>
<body>
  <h2>Outbound Call API Tester</h2>
  <div class="hint">Tip: Values are saved to your browser (localStorage). Press Ctrl+Enter to send.</div>

  <label>Endpoint URL
    <input id="url" value="http://localhost:8787/forward?url=https://outbound-call-985795046666.us-central1.run.app/make_call/" />
  </label>

  <div class="row">
    <div>
      <label>From Number
        <input id="from_number" value="+12314987814" />
      </label>
    </div>
    <div>
      <label>To Number
        <input id="to_number" value="+923349589089" />
      </label>
    </div>
  </div>

  <label>Mission
    <input id="mission" value="Lead generation" />
  </label>

  <label>Organisation ID
    <input id="organisation_id" value="jazzcash" />
  </label>

  <label>Campaign ID
    <input id="campaign_id" value="EFU Sales" />
  </label>

  <label>Booking ID
    <input id="booking_id" type="number" value="35" />
  </label>

  <label>Customer Name
    <input id="customer_name" value="Arham Anjum" />
  </label>

  <button id="send">Make Call</button>

  <pre id="out"></pre>

  <script>
    const ids = [
      "url",
      "from_number",
      "to_number",
      "mission",
      "organisation_id",
      "campaign_id",
      "booking_id",
      "customer_name"
    ];

    // Restore saved values
    ids.forEach(id => {
      const v = localStorage.getItem("makecall_" + id);
      if (v !== null) document.getElementById(id).value = v;
    });

    // Persist changes
    ids.forEach(id => {
      document.getElementById(id).addEventListener("change", e => {
        localStorage.setItem("makecall_" + id, e.target.value);
      });
    });

    async function send() {
      const url = document.getElementById("url").value.trim();
      const body = {
        from_number: document.getElementById("from_number").value.trim(),
        to_number: document.getElementById("to_number").value.trim(),
        mission: document.getElementById("mission").value.trim(),
        organisation_id: document.getElementById("organisation_id").value.trim(),
        campaign_id: document.getElementById("campaign_id").value.trim(),
        booking_id: Number(document.getElementById("booking_id").value),
        customer_name: document.getElementById("customer_name").value.trim()
      };

      const out = document.getElementById("out");
      out.textContent = "Sending request...";

      try {
        const res = await fetch(url, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(body)
        });

        const text = await res.text();
        let pretty;
        try { pretty = JSON.stringify(JSON.parse(text), null, 2); } catch { pretty = text; }

        out.textContent = `Status: ${res.status} ${res.statusText}\n\nResponse:\n${pretty}`;
      } catch (err) {
        out.textContent = "Error: " + err.message;
      }
    }

    document.getElementById("send").addEventListener("click", send);
    document.addEventListener("keydown", (e) => { if (e.ctrlKey && e.key === "Enter") send(); });
  </script>
</body>
</html>

