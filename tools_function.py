# Updated tools with more conservative lead marking
async def getDefaultTools():
    '''Enhanced tools with stricter lead qualification criteria'''
    return [
        {
            "type": "function",
            "name": "call_later",
            "description": '''
            Call this function ONLY when the customer explicitly requests to be called back later.
            DO NOT INVOKE IT IN ANY OTHER CIRCUMSTANCE.
            
            WHEN TO CALL:
            - Customer says "baad mein call kariye" / "call me later"
            - Customer says they are busy: "abhi busy hun" / "I'm busy right now"
            - Customer asks to reschedule: "dusre waqt call kar sakte hain?"
            - Customer says "not a good time" / "abhi acha waqt nahi hai"
            
            PROCESS:
            1. Ask: "Kya mei apko baad mein call karun? Kab convenient hoga?"
            2. Wait for their specific date/time response
            3. Confirm the callback time with them
            4. Only then call this function
            
            DO NOT call this function if customer is just asking questions or showing hesitation.
            ''',
            "parameters": {
                "type": "object",
                "properties": {
                    "callback_time": {
                        "type": "string",
                        "description": '''
                        Exact date and time provided by customer for callback.
                        Format: DD-MM-YYYY HH:MM AM/PM
                        Must be customer's specified time, NOT your suggestion.
                        '''
                    }
                },
                "required": ["callback_time"]
            }
        },
        
        {
            "type": "function",
            "name": "mark_lead_as_successful",
            "description": '''
            EXTREMELY IMPORTANT - Call this function ONLY under these STRICT conditions:
            
            MANDATORY REQUIREMENTS (ALL must be met):
            1. You have completed the FULL script presentation (opening, pitch, qualification, offer)
            2. You have asked the CLOSING QUESTION: "kia aap is sahulat main shamil hona chahay ge?"
            3. Customer has given EXPLICIT POSITIVE CONFIRMATION to this closing question
            4. Customer has confirmed they understand it's Rs. 2,950 annually from Jazz Cash account
            5. You have mentioned the 14-day free-look period and contact details
            
            EXPLICIT POSITIVE RESPONSES that qualify:
            - "Haan" / "Yes" / "Ji haan" 
            - "Main shamil hona chahta hun" / "I want to join"
            - "Theek hai, kar dete hain" / "Okay, let's do it"
            - "Agreement hai" / "I agree"
            - "Confirm kar dete hain" / "Let's confirm it"
            
            DO NOT CALL for these responses:
            - General interest: "acha hai" / "sounds good"
            - Questions: "kitne paise hain?" / "what's the cost?"
            - Thinking: "sochta hun" / "let me think"
            - Vague positivity: "interesting" / "dilchasp hai"
            - Asking for more info: "aur batayiye" / "tell me more"
            
            REMEMBER: This function should only be called AFTER the complete script flow and explicit agreement to join. 
            ''',
            "parameters": {
                "type": "object",
                "properties": {
                    "customer_confirmation": {
                        "type": "string",
                        "description": "The exact words customer used to confirm their agreement to join the plan"
                    },
                    "closing_question_asked": {
                        "type": "boolean", 
                        "description": "Confirm you asked the closing question before customer agreed"
                    },
                    "annual_price_confirmed": {
                        "type": "boolean",
                        "description": "Confirm customer understands the Rs. 2,950 annual price"
                    }
                },
                "required": ["closing_question_asked", "annual_price_confirmed"]
            }
        }
    ]