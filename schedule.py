from apscheduler.schedulers.background import BackgroundScheduler
from datetime import datetime
import psycopg2
import requests
from dotenv import load_dotenv
import os, json
from zoneinfo import ZoneInfo
load_dotenv()

import json
from google.cloud import pubsub_v1

# Configuration
PROJECT_ID = "noble-courier-418207"  # Replace with your GCP project
TOPIC_NAME = "call-orchestrator"        # Your Pub/Sub topic name
FROM_NUMBER = "+12314987814" 
        
db_params = {
'dbname': os.getenv('dbname'),
'user': os.getenv('user'),
'password': os.getenv('password'),
'host':os.getenv('host'),
'port': os.getenv('port')
}

api_url = os.getenv('api_url')


# Function to execute API calls
def execute_api_call(number_from, number_to, mission, organisation_id, campaign_id, booking_id, customer_name):
    try:
        
        # Create calls
        publisher = pubsub_v1.PublisherClient()
        topic_path = publisher.topic_path(PROJECT_ID, TOPIC_NAME)
    
        print(f"Publishing rescheduled calls to Pub/Sub...")
        
        call = {
            "from_number": FROM_NUMBER,
            "to_number": number_to,
            "mission": mission,
            "organisation_id": organisation_id,
            "campaign_id": campaign_id,
            "booking_id": booking_id,
            "customer_name": customer_name
        }
        
        message_data = json.dumps(call).encode("utf-8")
        future = publisher.publish(topic_path, message_data)
        message_id = future.result()
        return 'completed'
    except Exception as e:
        print(f"Error during API call: {e}")
        return 'failed'

# Function to check and execute scheduled jobs
def process_scheduled_jobs():
    try:
        conn = None 
        cursor = None

        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()

        # Query for jobs that need to be executed
        query = """
            SELECT call_id, schedule_time
            FROM chatbot.call_info
            WHERE schedule_time <= (NOW() AT TIME ZONE 'Asia/Karachi') AND reschedule_status = true;
            """
        cursor.execute(query)
        jobs = cursor.fetchall()
        for job in jobs:
            print(job)
            call_id, schedule_time = job

            # Update job status in the database
            select_query = """
            SELECT to_number, mission, campaign_id, organisation_id, customer_name, booking_id 
            FROM chatbot.call_logs WHERE call_id = %s
            """
            cursor.execute(select_query, (call_id))
            conn.commit()
            number_to, mission, campaign_id, organisation_id, customer_name, booking_id = cursor.fetchone()
            print(f"API call with Booking ID: {call_id} for number: {number_to} made successfully.")
            status = execute_api_call(FROM_NUMBER, number_to, mission, organisation_id, campaign_id, booking_id, customer_name)

    except Exception as e:
        print(f"Error processing scheduled jobs: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Start the scheduler
scheduler = BackgroundScheduler()
scheduler.add_job(process_scheduled_jobs, 'interval', seconds=30, max_instances = 2)  # Check every 30 seconds
scheduler.start()

print("Scheduler started...")
try:
    while True:
        pass  # Keep the script running
except (KeyboardInterrupt, SystemExit):
    scheduler.shutdown()
    print("Scheduler stopped.")
